# LSM Hide: G<PERSON><PERSON><PERSON> pháp lai eBPF cho ẩn tiến trình

## Mô tả
**[LSM Hide]** (Hybrid eBPF Cloak – Áo choàng eBPF lai) là một công cụ bảo mật Linux tiên tiến, kết hợp **[LSM]** (Linux Security Module – Mô-đun bảo mật Linux) và **[eBPF]** (Extended Berkeley Packet Filter – Bộ lọc gói mở rộng) để tạo thành một giải pháp bảo vệ và ẩn tiến trình đa lớp. Công cụ này giúp ẩn tiến trình khỏi các công cụ giám sát thông thường, bảo vệ chúng khỏi phương pháp phân tích pháp y và các nỗ lực gỡ lỗi.

## Tính năng chính
- **Ẩn tiến trình đa lớp**: Ẩn hoàn toàn khỏi các lệnh liệt kê (`/proc`, `ps`, `top`, `lsof`, ...)
- **<PERSON>ả<PERSON> vệ khỏi gỡ lỗi**: Ngăn chặn `ptrace`, `gdb` và các công cụ phân tích khác
- **Kiểm soát truy cập**: Kiểm soát chi tiết truy cập vào đối tượng nhạy cảm thông qua **[LSM hooks]** (móc bảo mật Linux)
- **Giả mạo danh tính**: Thay đổi **[UID/GID]** (User ID/Group ID – mã định danh người dùng/nhóm) để che giấu chủ sở hữu tiến trình thật
- **Chặn syscall nguy hiểm**: Phát hiện và ngăn chặn các **[syscall]** (lời gọi hệ thống) như `ptrace`, `perf_event_open`, `bpf`, `process_vm_readv`, `process_vm_writev`
- **Tối ưu hiệu suất**: Tác động tối thiểu đến hiệu suất hệ thống (< 2% CPU, < 10MB RAM)

## Kiến trúc kỹ thuật

### Thành phần cốt lõi
LSM Hide được xây dựng từ nhiều thành phần hoạt động cùng nhau:

1. **Chương trình BPF (`lsm_hide.bpf.c`)**: Mã chính thực thi trong nhân Linux
   - LSM hooks để kiểm soát truy cập `/proc` và bảo vệ tiến trình
   - Tracepoints để theo dõi và chặn các syscall nguy hiểm
   - KProbes để can thiệp vào syscall lấy UID/GID

2. **Maps BPF**: Cấu trúc dữ liệu lưu trữ thông tin quan trọng
   - `hidden_pids`: Danh sách các PID cần ẩn
   - `fake_cred_map`: Thông tin UID/GID giả cho từng tiến trình
   - `stats_map`: Thống kê các sự kiện bảo mật
   - `quota_cg`: Giới hạn số lượng thao tác bị chặn

3. **Công cụ không gian người dùng**
   - `lsm_hide_loader`: Nạp và quản lý chương trình BPF
   - `lsm_ctl`: Công cụ dòng lệnh để điều khiển và quản trị

4. **Dịch vụ hệ thống**
   - `lsm_hide.service`: Quản lý khởi động tự động
   - `lsm_pid_cleanup.service`: Dọn dẹp các PID không còn tồn tại

### Mô hình bảo vệ đa lớp
LSM Hide sử dụng mô hình bảo vệ nhiều tầng:

1. **Tầng 1: LSM Hooks** - Can thiệp vào các quyết định bảo mật của kernel
2. **Tầng 2: eBPF Tracepoints** - Theo dõi và lọc các syscall nguy hiểm
3. **Tầng 3: Seccomp-BPF** - Tạo bộ lọc syscall dựa trên seccomp
4. **Tầng 4: Cấu hình bảo mật hệ thống** - Thiết lập YAMA ptrace_scope, vô hiệu hóa core dump

## Cơ chế hoạt động

### 1. Ẩn tiến trình
Để ẩn một tiến trình, LSM Hide sử dụng các phương pháp sau:

1. **Can thiệp `/proc`**: Sử dụng LSM hooks `hide_proc_pid_lookup` và `hide_proc_pid_readdir` để ngăn truy cập vào thư mục `/proc/[pid]`

2. **Lọc kết quả `getdents64`**: Can thiệp vào syscall này để loại bỏ các mục liên quan đến PID cần ẩn khi liệt kê thư mục

3. **Ngăn chặn kiểm tra trực tiếp**: Chặn các phương pháp kiểm tra tiến trình thông qua `task_struct`

### 2. Giả mạo UID/GID

1. **Thay đổi kết quả syscall**: Sử dụng kprobes để can thiệp vào các syscall như `getuid`, `geteuid`, `getgid`, `getegid`

2. **Giả mạo thông tin trong `/proc`**: Sửa đổi nội dung hiển thị của `/proc/[pid]/status` để thay đổi thông tin Uid/Gid

3. **Quản lý thông tin giả mạo**: Lưu trữ và quản lý UID/GID giả trong `fake_cred_map`

### 3. Chống gỡ lỗi và phân tích

1. **Chặn ptrace**: Ngăn chặn gỡ lỗi bằng cách từ chối tất cả các cuộc gọi `ptrace` đến tiến trình được bảo vệ

2. **Bảo vệ bộ nhớ**: Chặn `process_vm_readv` và `process_vm_writev` để ngăn đọc/ghi bộ nhớ trực tiếp

3. **Chặn phân tích hiệu suất**: Ngăn chặn `perf_event_open` để tránh thu thập dữ liệu hiệu suất

4. **Bảo vệ eBPF**: Ngăn chặn syscall `bpf` để tránh can thiệp vào cơ chế bảo vệ

5. **Cấu hình mức độ bảo vệ**: Hỗ trợ 4 mức độ bảo vệ (0-3) với mỗi mức bật thêm các tính năng bảo vệ

## Yêu cầu hệ thống
- **Linux kernel 5.10+** (khuyến nghị 5.15+ cho đầy đủ tính năng)
- **Thư viện phát triển**: `libelf-dev`, `libbpf-dev`, `clang`, `llvm`, `libseccomp-dev`
- **Quyền hạn**: `CAP_SYS_ADMIN` hoặc RLIMIT_MEMLOCK đủ lớn
- **BTF (BPF Type Format)**: Cần thiết cho tính năng CO-RE (Compile Once - Run Everywhere)

## Cài đặt

### Cài đặt phụ thuộc
```bash
apt install -y build-essential clang llvm libelf-dev libbpf-dev libseccomp-dev
```

### Biên dịch
```bash
make clean && make
```

### Cài đặt
```bash
sudo make install
```

## Cách sử dụng

### Khởi chạy dịch vụ LSM Hide
```bash
sudo systemctl start lsm_hide
```

### Thêm PID vào danh sách ẩn
```bash
sudo lsm_ctl add 1234
```

### Hiển thị danh sách PID đang được ẩn
```bash
sudo lsm_ctl list
```

### Thiết lập UID/GID giả cho tiến trình
```bash
sudo lsm_ctl fakecred 1000 1000
```

### Hiển thị danh sách syscall nguy hiểm bị chặn
```bash
sudo lsm_ctl syscalls
```

### Kiểm tra trạng thái chặn của một syscall cụ thể
```bash
sudo lsm_ctl check_syscall ptrace
```

## Tùy chọn khi khởi động
Khi khởi động `lsm_hide_loader`, bạn có thể cung cấp các tùy chọn sau:
- `-v`: Chế độ chi tiết
- `-q <quota>`: Giới hạn số thao tác bị chặn
- `-d <level>`: Đặt mức bảo vệ debug (0-3)
  - **Mức 0**: Tắt bảo vệ
  - **Mức 1**: Cơ bản (LSM hooks)
  - **Mức 2**: Trung bình (LSM + Tracepoints)
  - **Mức 3**: Cao (LSM + Tracepoints + Seccomp)
- `-y <scope>`: Đặt giá trị yama_ptrace_scope
- `-c`: Tắt việc tạo core dump
- `-u <uid>`: Thiết lập UID giả mạo mặc định
- `-g <gid>`: Thiết lập GID giả mạo mặc định
- `-S <mode>`: Chọn chế độ chặn syscall nguy hiểm
  - `seccomp`: Chỉ dùng bộ lọc seccomp
  - `tracepoint`: Chỉ dùng eBPF tracepoint
  - `both`: Dùng cả hai (mặc định)
  - `none`: Tắt tính năng chặn syscall

## Mô hình hiệu suất và tác động
- **Overhead CPU**: < 2% trong hầu hết các trường hợp
- **Sử dụng bộ nhớ**: < 10MB trong không gian kernel và user
- **Giới hạn dịch vụ**: Cấu hình với `MemoryLimit=50M` và `CPUQuota=10%`
- **Khả năng tương thích**: Đã được kiểm nghiệm trên các kernel từ 5.10 đến 6.8

## Hạn chế và rủi ro
- **Phụ thuộc kernel**: Một số tính năng có thể không hoạt động trên kernel cũ
- **Không hoàn toàn vô hình**: Kỹ thuật phân tích bộ nhớ trực tiếp nâng cao có thể phát hiện
- **Xung đột tiềm ẩn**: Có thể xung đột với các mô-đun LSM khác
- **Quản lý PID**: Cần dọn dẹp đúng cách các PID không còn tồn tại

## Gỡ lỗi và khắc phục sự cố
- Sử dụng `sudo journalctl -u lsm_hide.service` để xem log của dịch vụ
- Kiểm tra `/sys/fs/bpf/lsm_hide` để xem các map BPF được pin
- Sử dụng `bpftool prog list` để liệt kê các chương trình BPF đang hoạt động
- Sử dụng `lsm_ctl stats` để xem thống kê về các sự kiện bảo mật
- Sử dụng `lsm_ctl cleanup` để dọn dẹp các PID không còn tồn tại

## License
LSM Hide được phân phối dưới giấy phép MIT. Xem tệp LICENSE để biết thêm chi tiết.