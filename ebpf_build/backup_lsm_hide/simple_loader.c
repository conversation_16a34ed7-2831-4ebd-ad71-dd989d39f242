#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>
#include <signal.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "output/lsm_hide_simple.skel.h"

static volatile bool exiting = false;

static void sig_handler(int sig)
{
    exiting = true;
}

static int libbpf_print_fn(enum libbpf_print_level level, const char *format, va_list args)
{
    return vfprintf(stderr, format, args);
}

int main(int argc, char **argv)
{
    struct lsm_hide_simple_bpf *skel;
    int err;

    /* Set up libbpf errors and debug info callback */
    libbpf_set_print(libbpf_print_fn);

    /* Bump RLIMIT_MEMLOCK to allow BPF sub-system to do anything */
    struct rlimit rlim_new = {
        .rlim_cur = RLIM_INFINITY,
        .rlim_max = RLIM_INFINITY,
    };

    if (setrlimit(RLIMIT_MEMLOCK, &rlim_new)) {
        fprintf(stderr, "Failed to increase RLIMIT_MEMLOCK limit!\n");
        return 1;
    }

    /* Open BPF application */
    skel = lsm_hide_simple_bpf__open();
    if (!skel) {
        fprintf(stderr, "Failed to open BPF skeleton\n");
        return 1;
    }

    /* Load & verify BPF programs */
    err = lsm_hide_simple_bpf__load(skel);
    if (err) {
        fprintf(stderr, "Failed to load and verify BPF skeleton: %d\n", err);
        goto cleanup;
    }

    /* Attach BPF programs */
    err = lsm_hide_simple_bpf__attach(skel);
    if (err) {
        fprintf(stderr, "Failed to attach BPF skeleton: %d\n", err);
        goto cleanup;
    }

    printf("Successfully loaded and attached eBPF LSM program!\n");
    printf("Program is now active. Press Ctrl+C to exit.\n");

    /* Set up signal handlers */
    signal(SIGINT, sig_handler);
    signal(SIGTERM, sig_handler);

    /* Test: Add current PID to hidden_pid_map for testing */
    if (argc > 1) {
        int test_pid = atoi(argv[1]);
        int value = 1;
        int map_fd = bpf_map__fd(skel->maps.hidden_pid_map);
        
        err = bpf_map_update_elem(map_fd, &test_pid, &value, BPF_ANY);
        if (err) {
            fprintf(stderr, "Failed to add PID %d to hidden map: %d\n", test_pid, err);
        } else {
            printf("Added PID %d to hidden processes map\n", test_pid);
        }
    }

    /* Keep program running */
    while (!exiting) {
        sleep(1);
    }

    printf("\nDetaching and cleaning up...\n");

cleanup:
    lsm_hide_simple_bpf__destroy(skel);
    return err < 0 ? -err : 0;
}
