# Simple Makefile for testing eBPF LSM compilation
OUTPUT := ./output
CLANG ?= clang
BPFTOOL ?= bpftool
CC ?= gcc

# BPF compilation flags
BPF_CFLAGS := -g -O2 -Wall -target bpf
BPF_CFLAGS += -D__TARGET_ARCH_x86_64
BPF_CFLAGS += -DKBUILD_MODNAME='"lsm_hide_simple"'
BPF_CFLAGS += -I/usr/include/bpf

# User-space compilation flags
CFLAGS := -g -Wall -I$(OUTPUT)
LIBS := -lbpf -lelf -lz

# Create output directory
$(OUTPUT):
	mkdir -p $(OUTPUT)

# Compile simple BPF program
$(OUTPUT)/lsm_hide_simple.bpf.o: lsm_hide_simple.bpf.c vmlinux.h | $(OUTPUT)
	$(CLANG) $(BPF_CFLAGS) -c $< -o $@

# Generate skeleton
$(OUTPUT)/lsm_hide_simple.skel.h: $(OUTPUT)/lsm_hide_simple.bpf.o
	$(BPFTOOL) gen skeleton $< > $@

# Compile loader
$(OUTPUT)/simple_loader: simple_loader.c $(OUTPUT)/lsm_hide_simple.skel.h
	$(CC) $(CFLAGS) $< -o $@ $(LIBS)

# Test compilation
test: $(OUTPUT)/lsm_hide_simple.bpf.o
	@echo "Simple eBPF LSM compilation successful!"
	@file $(OUTPUT)/lsm_hide_simple.bpf.o

# Build all
all: $(OUTPUT)/simple_loader

clean:
	rm -rf $(OUTPUT)

.PHONY: test clean all
