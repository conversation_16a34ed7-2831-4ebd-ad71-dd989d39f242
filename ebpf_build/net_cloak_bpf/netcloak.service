[Unit]
Description=Dị<PERSON> v<PERSON> ẩn và kiểm soát lưu lượng mạng - Net Cloak
After=network.target

[Service]
Type=simple
ExecStartPre=/bin/mkdir -p /sys/fs/bpf/net_cloak
ExecStart=/usr/sbin/attach_net_cloak -i eth0
Restart=on-failure
RestartSec=5
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
WorkingDirectory=/usr/lib/bpf

# Bảo mật
CapabilityBoundingSet=CAP_NET_ADMIN CAP_BPF CAP_PERFMON CAP_SYS_ADMIN CAP_SYS_RESOURCE
AmbientCapabilities=CAP_NET_ADMIN CAP_BPF CAP_PERFMON CAP_SYS_ADMIN CAP_SYS_RESOURCE
ProtectSystem=full
ProtectHome=true
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target 
