# SPDX-License-Identifier: (GPL-2.0 OR BSD-2-Clause)

LIBDIR ?= /usr/lib/bpf
BINDIR ?= /usr/sbin
CONFDIR ?= /etc/lsm_hide
KCONFIG ?= /boot/config-$(shell uname -r)
INSTALLDIR ?= output

OUTPUT := ./output

LIBBPF_SRC := ../libbpf/src
LIBBPF_OBJ := $(abspath $(OUTPUT)/libbpf.a)

INCLUDES := -I$(OUTPUT) -I../libbpf/include/uapi -I./include -I../headers/ -I/usr/include/bpf
CFLAGS := -g -Wall
INSTALL ?= install

ARCH ?= $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/' | sed 's/ppc64le/powerpc/' | sed 's/mips.*/mips/')
VMLINUX := ./vmlinux.h

# Attempt to detect libbpf from system
ifeq ($(wildcard $(LIBBPF_SRC)/*),)
	LIBBPF_FLAGS := $(shell pkg-config --cflags libbpf 2>/dev/null)
	LIBBPF_LINK := $(shell pkg-config --libs libbpf 2>/dev/null)
else
	LIBBPF_LINK := $(LIBBPF_OBJ)
endif

# Verify libbpf is available
$(shell echo "Checking for libbpf..." >&2)
$(if $(LIBBPF_LINK),,$(error No libbpf found, please install libbpf-dev or compile libbpf from source))

# Define flags for BPF program
BPF_CFLAGS := -g -O2 -Wall -target bpf
BPF_CFLAGS += -D__TARGET_ARCH_x86_64
BPF_CFLAGS += -DKBUILD_MODNAME='"lsm_hide"'
BPF_CFLAGS += -I/usr/include/bpf
# Add kernel version definition (6.8.0)
BPF_CFLAGS += -D__KERNEL_VERSION_MAJOR=6 -D__KERNEL_VERSION_MINOR=8 -D__KERNEL_VERSION_PATCH=0
# Add debug output for helping troubleshooting
BPF_CFLAGS += -DDEBUG_VERBOSE

# Additional flags to enable debugging and features
ifdef DEBUG
	BPF_CFLAGS += -DDEBUG
endif

# Output directories
OUTPUT_DIRS := $(OUTPUT)/obj

# BPF object files
BPF_OBJS := $(OUTPUT)/obj/lsm_hide.bpf.o

# User-space object files
USER_OBJS := $(OUTPUT)/obj/lsm_hide_loader.o $(OUTPUT)/obj/lsm_ctl.o

# Include seccomp for process hiding control
SECCOMP_LINK := $(shell pkg-config --libs libseccomp 2>/dev/null || echo -lseccomp)

# Tools and other required components
CLANG ?= clang
LLVM_STRIP ?= llvm-strip
BPFTOOL ?= $(shell which bpftool || echo /usr/sbin/bpftool)

# Default target
all: $(OUTPUT)/lsm_hide_loader

# Check for vmlinux header availability
.PHONY: check_vmlinux
check_vmlinux:
	@if [ ! -f $(VMLINUX) ]; then \
		echo "Generating BTF vmlinux.h..."; \
		$(BPFTOOL) btf dump file /sys/kernel/btf/vmlinux format c > $(VMLINUX); \
		if [ $$? -ne 0 ]; then \
			echo "BTF data is not available. Kernel may not support it."; \
			exit 1; \
		fi; \
	fi

# Create output directories
.PHONY: dirs
dirs:
	mkdir -p $(OUTPUT_DIRS)

# Compile BPF program
$(OUTPUT)/obj/%.bpf.o: %.bpf.c check_vmlinux constants.h lsm_defs.h | dirs
	$(CLANG) $(BPF_CFLAGS) $(INCLUDES) -c $< -o $@

# Generate skeleton from BPF object
$(OUTPUT)/%.skel.h: $(OUTPUT)/obj/%.bpf.o | dirs
	$(BPFTOOL) gen skeleton $< > $@

# Compile user-space programs
$(OUTPUT)/obj/%.o: %.c $(OUTPUT)/lsm_hide.skel.h constants.h lsm_defs.h | dirs
	$(CC) $(CFLAGS) $(INCLUDES) $(LIBBPF_FLAGS) -c $< -o $@

# Link user-space programs
$(OUTPUT)/lsm_hide_loader: $(OUTPUT)/obj/lsm_hide_loader.o | dirs
	$(CC) $(CFLAGS) $^ $(LIBBPF_LINK) $(SECCOMP_LINK) -lelf -lz -o $@

$(OUTPUT)/lsm_ctl: $(OUTPUT)/obj/lsm_ctl.o | dirs
	$(CC) $(CFLAGS) $^ $(LIBBPF_LINK) $(SECCOMP_LINK) -lelf -lz -o $@

# Install targets
install:
	$(INSTALL) -d $(INSTALLDIR)
	$(INSTALL) -m 0755 $(OUTPUT)/lsm_hide_loader $(INSTALLDIR)/
	$(INSTALL) -m 0755 $(OUTPUT)/lsm_ctl $(INSTALLDIR)/
	$(INSTALL) -d $(INSTALLDIR)/systemd
	$(INSTALL) -m 0644 lsm_hide.service $(INSTALLDIR)/systemd/
	$(INSTALL) -m 0644 lsm_pid_cleanup.service $(INSTALLDIR)/systemd/
	$(INSTALL) -m 0644 lsm_pid_cleanup.timer $(INSTALLDIR)/systemd/

# Check environment
.PHONY: check_env
check_env:
	@echo "Checking environment..."
	@echo "Kernel: $(shell uname -r)"
	@echo "Architecture: $(shell uname -m)"
	@echo "Clang version: $(shell $(CLANG) --version | head -n1)"
	@echo "BPFTool version: $(shell $(BPFTOOL) version 2>&1 | head -n1)"
	@echo "LibBPF: $(shell pkg-config --modversion libbpf 2>/dev/null || echo 'unknown')"
	@echo "Seccomp: $(shell pkg-config --modversion libseccomp 2>/dev/null || echo 'unknown')"
	@echo "BTF available: $(shell test -f /sys/kernel/btf/vmlinux && echo 'yes' || echo 'no')"

# Clean targets
clean:
	rm -rf $(OUTPUT)

.PHONY: all clean install check_env 