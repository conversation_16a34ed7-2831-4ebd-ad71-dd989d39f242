Checking for libbpf...
mkdir -p ./output/obj
clang -g -O2 -Wall -target bpf -D__TARGET_ARCH_x86_64 -DKBUILD_MODNAME='"lsm_hide"' -I/usr/include/bpf -D__KERNEL_VERSION_MAJOR=6 -D__KERNEL_VERSION_MINOR=8 -D__KERNEL_VERSION_PATCH=0 -DDEBUG_VERBOSE -I./output -I../libbpf/include/uapi -I./include -I../headers/ -I/usr/include/bpf -c lsm_hide.bpf.c -o output/obj/lsm_hide.bpf.o
/usr/sbin/bpftool gen skeleton output/obj/lsm_hide.bpf.o > output/lsm_hide.skel.h
cc -g -Wall -I./output -I../libbpf/include/uapi -I./include -I../headers/ -I/usr/include/bpf  -c lsm_hide_loader.c -o output/obj/lsm_hide_loader.o
lsm_hide_loader.c:254:12: warning: ‘set_fake_cred_for_pid’ defined but not used [-Wunused-function]
  254 | static int set_fake_cred_for_pid(struct bpf_object *obj, __u32 pid, __u32 uid, __u32 gid, int enabled)
      |            ^~~~~~~~~~~~~~~~~~~~~
cc -g -Wall output/obj/lsm_hide_loader.o -lbpf -lseccomp -lelf -lz -o output/lsm_hide_loader
rm output/obj/lsm_hide.bpf.o output/lsm_hide.skel.h
