/**
 * LSM Hide - Đ<PERSON><PERSON> nghĩa các cấu trúc và macro cần thiết
 */

#ifndef _LSM_DEFS_H
#define _LSM_DEFS_H

#include "constants.h"
#include <linux/limits.h>
#include <sys/resource.h>

/* Đ<PERSON>nh nghĩa đường dẫn */
#define YAMA_PTRACE_SCOPE_PATH "/proc/sys/kernel/yama/ptrace_scope"
#define CORE_PATTERN_PATH "/proc/sys/kernel/core_pattern"
#define SUID_DUMPABLE_PATH "/proc/sys/fs/suid_dumpable"

/* <PERSON><PERSON><PERSON> nghĩa cấu trúc syscall block config */
struct syscall_block_config {
    int enabled;
    int threshold;  /* Số lần gọi trước khi block */
    int persist;    /* Thời gian (giây) giữa các cảnh báo */
    int use_seccomp;     /* Sử dụng seccomp filter */
    int use_tracepoint;  /* Sử dụng tracepoint */
};

/* <PERSON><PERSON><PERSON> hằng số khác có thể thêm vào đây */
#define NUM_DANGEROUS_SYSCALLS 5

/* Khai báo typedef cho các kiểu dữ liệu */
typedef unsigned int __u32;
typedef unsigned long long __u64;

/* Chuyển định nghĩa các map và function vào lsm_hide.bpf.c */

#endif /* _LSM_DEFS_H */ 