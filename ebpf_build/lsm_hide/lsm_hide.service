[Unit]
Description=LSM-based Process Hiding Service
After=network.target
Documentation=https://github.com/example/lsm_hide

[Service]
Type=simple
# Thêm tùy chọn -d 3 (mứ<PERSON> b<PERSON><PERSON> vệ debug cao nhất), -y 2 (<PERSON><PERSON><PERSON> ptrace_scope admin), -c (vô hiệu hóa core dump)
ExecStart=/usr/local/sbin/lsm_hide_loader -v -q 10000 -d 3 -y 2 -c /sys/fs/cgroup/system.slice/
Restart=on-failure
RestartSec=5
KillMode=process
# Đảm bảo có quyền truy cập vào /sys/fs/bpf và sysctls
AmbientCapabilities=CAP_BPF CAP_SYS_ADMIN CAP_SYS_PTRACE CAP_PERFMON CAP_SYS_RESOURCE
CapabilityBoundingSet=CAP_BPF CAP_SYS_ADMIN CAP_SYS_PTRACE
# Chỉ định thư mục làm việc
WorkingDirectory=/etc/lsm_hide
# Giới hạn tài nguyên
MemoryLimit=50M
CPUQuota=10%
# Cài đặt hệ thống security
SystemCallFilter=~@clock @cpu-emulation @debug @module @mount @obsolete @privileged
SystemCallErrorNumber=EPERM
# Cho phép bật seccomp
SecureBits=keep-caps
NoNewPrivileges=no

[Install]
WantedBy=multi-user.target 