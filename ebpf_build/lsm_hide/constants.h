/* 
 * L<PERSON> Hide - <PERSON><PERSON><PERSON> nghĩa hằng số
 * File này định nghĩa các hằng số cần thiết cho mô-đun lsm_hide
 */

#ifndef _LSM_HIDE_CONSTANTS_H
#define _LSM_HIDE_CONSTANTS_H

/* Kernel version macros */
#ifndef KERNEL_VERSION
#define KERNEL_VERSION(a, b, c) (((a) << 16) + ((b) << 8) + (c))
#endif

/* Thêm định nghĩa phiên bản kernel */
#define LINUX_VERSION_CODE KERNEL_VERSION(__KERNEL_VERSION_MAJOR, __KERNEL_VERSION_MINOR, __KERNEL_VERSION_PATCH)

/* Filesystem magic numbers */
#define PROC_SUPER_MAGIC       0x9fa0

/* LSM Constants */
#define LSM_ID_CAPABILITY      0
#define LSM_ID_SELINUX         1
#define LSM_ID_SMACK           2
#define LSM_ID_TOMOYO          3
#define LSM_ID_APPARMOR        4
#define LSM_ID_YAMA            5
#define LSM_ID_LOADPIN         6
#define LSM_ID_SAFESETID       7
#define LSM_ID_LOCKDOWN        8
#define LSM_ID_BPF             9
#define LSM_ID_LANDLOCK        10

/* Error codes */
#define EPERM                  1      /* Operation not permitted */
#define ENOENT                 2      /* No such file or directory */
#define ESRCH                  3      /* No such process */
#define EACCES                 13     /* Permission denied */
#define EEXIST                 17     /* File exists */
#define EINVAL                 22     /* Invalid argument */

/* Signals */
#define SIGHUP                 1
#define SIGINT                 2
#define SIGQUIT                3
#define SIGILL                 4
#define SIGTRAP                5
#define SIGABRT                6
#define SIGBUS                 7
#define SIGFPE                 8
#define SIGKILL                9
#define SIGUSR1                10
#define SIGSEGV                11
#define SIGUSR2                12
#define SIGPIPE                13
#define SIGALRM                14
#define SIGTERM                15
#define SIGSTKFLT              16
#define SIGCHLD                17
#define SIGCONT                18
#define SIGSTOP                19

/* eBPF syscall numbers */
#define __NR_bpf               321

/* Process control */
#define PR_SET_PTRACER         0x59616d61
#define PR_SET_DUMPABLE        4

/* Event type definitions */
#define EVENT_SYSCALL_BLOCKED  20
#define EVENT_TASK_KILL        1
#define EVENT_TASK_GETPGID     2
#define EVENT_PTRACE_DENIED    3
#define EVENT_PTRACE_BLOCKED   4
#define EVENT_PRCTL_BLOCKED    5
#define EVENT_GETDENTS_FILTER  6
#define EVENT_UID_SPOOFING     7
#define EVENT_TASK_GETATTR     8
#define EVENT_PROC_STAT_SPOOF  9

/* Additional macros */
#ifndef __has_kernel_version_at_least
#define __has_kernel_version_at_least(a, b, c) \
    (LINUX_VERSION_CODE >= KERNEL_VERSION(a, b, c))
#endif

/* Các tùy chọn biên dịch có điều kiện */
#ifdef DEBUG_VERBOSE
#define DEBUG_PRINT(fmt, ...) bpf_printk(fmt, ##__VA_ARGS__)
#else
#define DEBUG_PRINT(fmt, ...)
#endif

#endif /* _LSM_HIDE_CONSTANTS_H */
